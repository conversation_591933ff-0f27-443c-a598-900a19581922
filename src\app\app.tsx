'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { supabase } from '../../supabase';
import '../globals.css';
import { AuthChangeEvent } from '@supabase/supabase-js';

type AppProps = {
  Component: React.ComponentType<{ user: { id: string; email: string } | null }>;
  pageProps: Record<string, unknown>;
}

const App = ({ Component, pageProps }: AppProps) => {
  const [user, setUser] = useState<null | { id: string; email: string }>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        if (error) {
          console.error('Error fetching session:', error.message);
          return;
        }
        if (data?.session) {
          setUser({
            id: data.session.user.id,
            email: data.session.user.email || '',
          });
        }
      } catch (err) {
        console.error('Error in fetchSession:', err);
      }
    };

    fetchSession();

    // Listen for changes to the authentication state (login, logout, session update)
    const { data: { subscription } } = supabase.auth.onAuthStateChange((
      _event: AuthChangeEvent,
      session
    ) => {
      if (session?.user) {
        setUser({
          id: session.user.id,
          email: session.user.email || '',
        });
        // Redirect to HomePage after login
        if (router.pathname === '/auth') {
          router.push('/HomePage');
        }
      } else {
        setUser(null);
        // Redirect to AuthPage if the user logs out
        if (router.pathname !== '/auth') {
          router.push('/auth');
        }
      }
    });

    // Cleanup on component unmount
    return () => {
      subscription?.unsubscribe();
    };
  }, [router]);

  // Render the component passed as props
  return <Component {...pageProps} user={user} />;
}

export default App;
