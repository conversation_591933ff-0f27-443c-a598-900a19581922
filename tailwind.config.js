module.exports = {
  content: [
    './src/**/*.{js,ts,jsx,tsx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      fontFamily: {
        handwritten: ['Indie Flower', 'cursive'],
      },
      animation: {
        'paper-float': 'paper-float 6s ease-in-out infinite',
        'fold-corner': 'fold-corner 0.5s ease-out',
      },
      keyframes: {
        'paper-float': {
          '0%, 100%': { 
            transform: 'translateY(0) rotate(-2deg) skew(-2deg)',
          },
          '50%': { 
            transform: 'translateY(-20px) rotate(2deg) skew(2deg)',
          },
        },
        'fold-corner': {
          '0%': { transform: 'scaleX(0)' },
          '100%': { transform: 'scaleX(1)' },
        },
      },
    },
  },
  plugins: [],
} 