'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '../../../supabase';
import { motion, AnimatePresence } from 'framer-motion';

const colors = {
  yellow: "bg-yellow-100 border-yellow-200",
  blue: "bg-blue-100 border-blue-200",
  pink: "bg-pink-100 border-pink-200",
  green: "bg-green-100 border-green-200",
  purple: "bg-purple-100 border-purple-200"
};

// Email validation function
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
};

// Password validation function
const validatePassword = (password: string): boolean => {
  // Password should be at least 8 characters long and contain both letters and numbers
  return password.length >= 8 && /(?=.*[A-Za-z])(?=.*\d)/.test(password);
};

const StickyNote = ({ children, color = "yellow", skewDeg = -2 }: { 
  children: React.ReactNode, 
  color?: keyof typeof colors,
  skewDeg?: number 
}) => {
  return (
    <motion.div
      initial={{ rotate: skewDeg - 5, y: 20, opacity: 0 }}
      animate={{ rotate: skewDeg, y: 0, opacity: 1 }}
      whileHover={{ rotate: skewDeg + 1, scale: 1.02 }}
      className={`relative w-96 ${colors[color]} p-8 rounded-sm`}
      style={{
        boxShadow: "2px 2px 15px rgba(0,0,0,0.15)",
        backgroundImage: "repeating-linear-gradient(0deg, transparent, transparent 20px, rgba(0,0,0,0.02) 20px, rgba(0,0,0,0.02) 21px)"
      }}
    >
      {/* Enhanced fold effect */}
      <div 
        className={`absolute top-0 right-0 w-16 h-16 
          transform rotate-[-3deg] bg-gradient-to-br 
          ${color === "yellow" ? "from-yellow-200/80 to-yellow-100/30" : "from-blue-200/80 to-blue-100/30"}`}
        style={{
          clipPath: "polygon(100% 0, 0 0, 100% 100%)",
        }}
      />
      {/* Enhanced pin effect */}
      <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-8 h-8">
        <motion.div 
          className="w-full h-full bg-red-500 rounded-full shadow-lg"
          whileHover={{ scale: 1.1, rotate: 10 }}
        >
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-red-600 rounded-full" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-red-400 rounded-full" />
        </motion.div>
      </div>
      {/* Tape effect */}
      <div className="absolute -top-2 left-8 w-20 h-6 bg-white/30 backdrop-blur-sm transform rotate-[-4deg]" 
        style={{ clipPath: "polygon(5% 0, 95% 0, 100% 100%, 0% 100%)" }}
      />
      {children}
    </motion.div>
  );
};

// const SocialLoginButton = ({ provider, icon, color, onClick, disabled = false }: {
//   provider: 'google' | 'github';
//   icon: string;
//   color: string;
//   onClick: () => void;
//   disabled?: boolean;
// }) => (
//   <motion.button
//     onClick={() => !disabled && onClick()}
//     whileHover={{ rotate: disabled ? 0 : 5, scale: disabled ? 1 : 1.05 }}
//     whileTap={{ scale: disabled ? 1 : 0.95 }}
//     className={`relative w-full p-4 mb-3 ${color} rounded-sm shadow-md transform rotate-[-1deg] 
//       font-handwritten text-lg flex items-center justify-center gap-3
//       ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
//   >
//     {icon}
//     <span>Continue with {provider}</span>
//     <div className="absolute top-0 right-0 w-8 h-8" 
//       style={{ clipPath: "polygon(100% 0, 0 0, 100% 100%)" }}
//     />
//   </motion.button>
// );

// const handleSocialLogin = async (provider: 'google' | 'github', setError: React.Dispatch<React.SetStateAction<string | null>>) => {
//   try {
//     const { error } = await supabase.auth.signInWithOAuth({
//       provider,
//       options: {
//         redirectTo: `${window.location.origin}/auth/callback`,
//         queryParams: {
//           access_type: 'offline',
//           prompt: 'consent',
//         }
//       }
//     });

//     if (error) throw error;

//     const { data, error: sessionError } = await supabase.auth.getSession();
//     if (sessionError) throw sessionError;

//     const user = data?.session?.user;

//     if (user?.email) {
//       const email = user.email.toLowerCase();
//       console.log('User email:', email);
//     } else {
//       console.error('Email not found in user session data.');
//     }
//   } catch (err) {
//     console.error(`${provider} login error:`, err);
//     setError('Social login failed. Please try again.');
//   }
// };

const handleEmailSignUp = async (email: string, password: string, setError: React.Dispatch<React.SetStateAction<string | null>>) => {
  if (password.length < 8 || !/(?=.*[A-Za-z])(?=.*\d)/.test(password)) {
    setError('Password must be at least 8 characters with letters and numbers');
    return;
  }

  const { error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${window.location.origin}/auth/callback`
    }
  });

  if (error) {
    setError(error.message);
    return;
  }
};

const handleEmailSignIn = async (email: string, password: string, setError: React.Dispatch<React.SetStateAction<string | null>>) => {
  const { error } = await supabase.auth.signInWithPassword({
    email,
    password
  });

  if (error) {
    setError(error.message);
    return;
  }
};

const AuthPage = () => {
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [isSignUp, setIsSignUp] = useState<boolean>(false);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        router.push('/');
      }
    };

    checkAuth();
  }, [router]);

  const handleSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      setLoading(false);
      return;
    }

    if (!validatePassword(password)) {
      setError('Password must contain at least 8 characters, including at least one letter and one number');
      setLoading(false);
      return;
    }

    try {
      const rateLimitKey = `auth_attempts_${email}`;
      const attempts = sessionStorage.getItem(rateLimitKey) || '0';
      if (parseInt(attempts) > 5) {
        setError('Too many attempts. Please try again later.');
        setLoading(false);
        return;
      }

      if (isSignUp) {
        await handleEmailSignUp(email, password, setError);
      } else {
        await handleEmailSignIn(email, password, setError);
      }
    } catch (err) {
      console.error('Auth error:', err);
      setError('An authentication error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-yellow-50 flex flex-col justify-center items-center text-center relative overflow-hidden p-8">
      {/* Floating background notes with skew */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className={`absolute w-32 h-32 ${['bg-yellow-200/30', 'bg-blue-200/30', 'bg-green-200/30', 'bg-pink-200/30', 'bg-purple-200/30'][i % 5]} rounded-sm backdrop-blur-sm`}
          animate={{
            y: [0, -20, 0],
            rotate: [i % 2 ? -3 : 3, i % 2 ? 3 : -3, i % 2 ? -3 : 3],
            skew: [i % 2 ? -2 : 2, i % 2 ? 2 : -2, i % 2 ? -2 : 2],
          }}
          transition={{
            duration: 6 + i,
            repeat: Infinity,
            delay: i * 0.5,
          }}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
        />
      ))}

      <AnimatePresence>
        <StickyNote color="yellow" skewDeg={-3}>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-6"
          >
            <h2 className="text-2xl font-handwritten mb-6 text-gray-800">
              {isSignUp ? '📝 Create New Note' : '🔓 Unlock Your Notes'}
            </h2>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <input
                  type="email"
                  placeholder="✉️ Email"
                  className="w-full p-3 bg-white/50 border-b-2 border-yellow-300 focus:border-yellow-500"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  autoComplete="email"
                  spellCheck="false"
                  autoCapitalize="none"
                />
              </div>

              <div className="space-y-2">
                <input
                  type="password"
                  placeholder="🔑 Password"
                  className="w-full p-3 bg-white/50 border border-gray-200 rounded-lg"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  autoComplete="current-password"
                  minLength={8}
                />
              </div>

              {error && (
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-red-500 text-sm"
                >
                  {error}
                </motion.p>
              )}

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                className={`w-full p-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg font-medium 
                  ${loading ? 'opacity-50 cursor-not-allowed' : 'hover:opacity-90'} transition-opacity`}
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-2 h-2 bg-white rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.4s' }} />
                  </div>
                ) : (
                  isSignUp ? 'Sign Up' : 'Log In'
                )}
              </motion.button>
            </form>

            <div className="mt-6 text-center">
              <button
                onClick={() => setIsSignUp(!isSignUp)}
                className="text-purple-600 text-sm font-medium hover:text-purple-700 transition-colors"
              >
                {isSignUp ? 'Already have an account? Log In' : "Don't have an account? Sign Up"}
              </button>
            </div>

            <div className="mt-6 space-y-3">
              {/* <SocialLoginButton 
                // provider="google"
                // icon="🌐"
                color="bg-white/80"
                onClick={() => handleSocialLogin('google', setError)}
                disabled={loading}
              /> */}
            </div>
          </motion.div>
        </StickyNote>
      </AnimatePresence>
    </div>
  );
};

export default AuthPage;
