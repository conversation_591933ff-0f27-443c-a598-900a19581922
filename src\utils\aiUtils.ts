import axios from 'axios';


const GROQ_API_URL = 'https://api.groq.com/openai/v1/embeddings';
const GROQ_API_KEY = process.env.NEXT_PUBLIC_GROQ_API_KEY;

if (!GROQ_API_KEY) {
  throw new Error('GROQ API key is not configured');
}

export const createEmbedding = async (text: string): Promise<number[]> => {
  try {
    const response = await axios.post(
      GROQ_API_URL,
      {
        model: "mixtral-8x7b-32768",
        input: text
      },
      {
        headers: {
          'Authorization': `Bearer ${GROQ_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response.data.data[0].embedding;
  } catch (error) {
    console.error('Groq embedding error:', error);
    return [];
  }
};

export const cosineSimilarity = (vecA: number[], vecB: number[]): number => {
  const dotProduct = vecA.reduce((acc, val, i) => acc + val * vecB[i], 0);
  const magnitudeA = Math.sqrt(vecA.reduce((acc, val) => acc + val * val, 0));
  const magnitudeB = Math.sqrt(vecB.reduce((acc, val) => acc + val * val, 0));
  
  return dotProduct / (magnitudeA * magnitudeB);
};

export const getSimilarityScore = async (text1: string, text2: string): Promise<number> => {
  const [embedding1, embedding2] = await Promise.all([
    createEmbedding(text1),
    createEmbedding(text2)
  ]);
  
  return cosineSimilarity(embedding1, embedding2);
};

export const findSimilarNotes = async (query: string, notes: any[]): Promise<any[]> => {
  const queryEmbedding = await createEmbedding(query);
  
  return notes
    .map(note => ({
      note,
      similarity: cosineSimilarity(queryEmbedding, note.embedding || [])
    }))
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, 5)
    .map(result => result.note);
};