import { useEffect } from 'react';
// import { createClient } from '@supabase/supabase-js';
import { supabase } from '../../supabase';

export const CollaborativeEditor: React.FC<{ noteId: string }> = ({ noteId }) => {
  const activeUsers: { id: string }[] = []; // Define activeUsers

  useEffect(() => {
    const channel = supabase
      .channel(`note:${noteId}`)
      .on('presence', { event: 'sync' }, () => {
        // Handle presence sync
      })
      .subscribe();

    return () => {
      channel.unsubscribe();
    };
  }, [noteId]);

  return (
    <div className="relative">
      {/* Show active users */}
      <div className="absolute top-2 right-2 flex -space-x-2">
        {activeUsers.map(user => (
          <div key={user.id} className="w-8 h-8 rounded-full bg-blue-500" />
        ))}
      </div>
      {/* Existing editor content */}
    </div>
  );
};