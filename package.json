{"name": "<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.1.5", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/realtime-js": "^2.11.3", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.46.1", "@tailwindcss/line-clamp": "^0.4.4", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@types/react-beautiful-dnd": "^13.1.8", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "dotenv": "^16.4.7", "framer-motion": "^11.11.15", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "lucide-react": "^0.456.0", "next": "15.0.3", "pg": "^8.13.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^5.1.0", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-dropzone": "^14.3.5", "react-feather": "^2.0.10", "react-hot-toast": "^2.5.1", "react-icons": "^5.3.0", "react-toastify": "^11.0.2", "tesseract.js": "^5.1.1", "uuid": "^11.0.3"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^20.17.6", "@types/pg": "^8.11.10", "@types/react": "^18.3.13", "@types/react-csv": "^1.1.10", "@types/react-dom": "^18.3.1", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8.4.49", "tailwindcss": "^3.4.14", "typescript": "^5"}}