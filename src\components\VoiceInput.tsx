import React, { useState, useCallback, useRef, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { Mic, Square, Loader2 } from 'lucide-react';
import { summarizeNote } from '../../groqAi';

declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

interface VoiceInputProps {
  onTranscriptionComplete: (note: { title: string; content: string; summary?: string; emoji?: string }) => Promise<void>;
}

// Optimized categories with most common keywords first
const contentCategories = {
  meeting: {
    keywords: ['meeting', 'call', 'conference'],
    emoji: '👥'
  },
  medical: {
    keywords: ['doctor', 'medical', 'appointment'],
    emoji: '👨‍⚕️'
  },
  exercise: {
    keywords: ['gym', 'workout', 'exercise'],
    emoji: '💪'
  },
  shopping: {
    keywords: ['shopping', 'buy', 'store'],
    emoji: '🛍️'
  },
  food: {
    keywords: ['food', 'lunch', 'dinner'],
    emoji: '🍽️'
  },
  default: {
    emoji: '📝'
  }
};

const VoiceInput: React.FC<VoiceInputProps> = ({ onTranscriptionComplete }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const recognitionRef = useRef<any>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout>();

  // Fast emoji detection using Set for O(1) lookup
  const keywordMap = useRef(new Map<string, string>());
  useEffect(() => {
    // Initialize keyword map once
    Object.entries(contentCategories).forEach(([, data]) => {
      if ('keywords' in data) {
        data.keywords.forEach(keyword => {
          keywordMap.current.set(keyword, data.emoji);
        });
      }
    });
  }, []);

  // Wrap 'processTranscript' in its own useCallback to avoid re-creation on each render
  const processTranscript = useCallback(async (rawTranscript: string) => {
    try {
      // Prevent duplicate processing
      if (isProcessing) return;
      setIsProcessing(true);

      // Clear any pending debounce
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // Process after small delay to prevent duplicates
      debounceTimerRef.current = setTimeout(async () => {
        const aiAnalysis = await summarizeNote(rawTranscript);

        await onTranscriptionComplete({
          title: rawTranscript.split('.')[0].trim(),
          content: rawTranscript,
          summary: aiAnalysis.summary,
          emoji: aiAnalysis.emoji
        });

        // Clear after successful processing
        clearRecording();
        setIsProcessing(false);
      }, 500);

    } catch (error) {
      console.error('Error processing transcript:', error);
      toast.error('Failed to process voice note');
      setIsProcessing(false);
    }
  }, [isProcessing, onTranscriptionComplete]);

  const clearRecording = () => {
    setTranscript('');
    setIsRecording(false);
    setIsProcessing(false);

    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }

    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
  };

  const startRecording = useCallback(() => {
    try {
      const Recognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new Recognition();

      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'en-US'; // Explicitly set language for better recognition

      recognitionRef.current.onstart = () => {
        setIsRecording(true);
        toast.success('Recording started');
      };

      recognitionRef.current.onresult = (event: any) => {
        const latestTranscript = Array.from(event.results)
          .map((result: any) => result[0].transcript)
          .join(' ');
        setTranscript(latestTranscript);
        processTranscript(latestTranscript); // Pass the transcript to processing
      };

      recognitionRef.current.onend = async () => {
        try {
          if (transcript) {
            // Process final transcript
            await processTranscript(transcript);
            // Clear after processing
            clearRecording();
            toast.success('Recording processed');
          }
        } catch (error) {
          console.error('Error processing recording:', error);
          toast.error('Failed to process recording');
        } finally {
          setIsRecording(false);
        }
      };

      recognitionRef.current.start();
    } catch  {
      toast.error('Speech recognition failed');
      setIsRecording(false);
    }
  }, [processTranscript, transcript]);

  const stopRecording = useCallback(() => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      clearRecording();
    }
  }, []);

  // Cleanup
  useEffect(() => {
    return () => {
      clearRecording();
    };
  }, []);

  return (
    <div className="flex flex-col items-center gap-2">
      <button
        onClick={isRecording ? stopRecording : startRecording}
        disabled={isProcessing}
        className="p-3 rounded-full bg-blue-500 text-white hover:bg-blue-600 
                   disabled:bg-gray-400 disabled:cursor-not-allowed
                   transition-colors duration-200 flex items-center gap-2"
      >
        {isRecording ? (
          <>
            <Square className="w-4 h-4" />
            <span>Stop</span>
          </>
        ) : isProcessing ? (
          <>
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>Processing...</span>
          </>
        ) : (
          <>
            <Mic className="w-4 h-4" />
            <span>Record</span>
          </>
        )}
      </button>

      {transcript && (
        <div className="p-4 bg-gray-50 rounded-lg shadow-sm mt-2 max-w-md w-full">
          <p className="text-sm text-gray-700 leading-relaxed">{transcript}</p>
        </div>
      )}
    </div>
  );
};

export default VoiceInput;
