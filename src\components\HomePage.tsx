'use client';
import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import { summarizeNote } from '../../groqAi';
import { useTheme } from '@/context/ThemeContext';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
// import { CSVLink } from 'react-csv';
// import { useDropzone } from 'react-dropzone';
import { Menu, Transition } from '@headlessui/react';
// import { ArrowUpTrayIcon} from '@heroicons/react/20/solid';
import { toast } from 'react-toastify';
import { SummaryModal} from '../components/SummaryModal';
import { CameraCapture} from '../components/CameraCapture';
import { ExportDropdown} from '../components/ExportDropdown';
import { getColorValue } from '../types/colorUtils'; // Adjust path as  // Adjust the path as necessary // Adjust the path accordingly
import { NoteColor } from '../types/notes';
import { StickyNote } from '../types/notes';
import { encryptionService } from '../utils/encryption';
import { Bell } from 'react-feather';
import VoiceInput from './VoiceInput';
import { NextApiRequest, NextApiResponse } from 'next';
// import { semanticSearch } from '../utils/semanticSearch';
// import { CollaborativeNote } from './CollaborativeNote';
import { EllipsisVerticalIcon, PencilIcon, TrashIcon, ShareIcon } from '@heroicons/react/24/outline';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);


import { DecryptedNote} from '@/utils/encryption';
// import { Bell as LucideBell } from 'lucide-react';
// NoteColor type is now imported from colorUtils

// Removed local StickyNote interface to resolve conflict with import

// Add new interfaces at top
interface Reminder {
  id: string;
  noteId: number;
  noteTitle: string;
  time: Date;
  isRead: boolean;
  content: string; // Add content property
}

const HomePage: React.FC = () => {
  const convertToStickyNote = (note: DecryptedNote | null): StickyNote | null => {
    if (!note) return null;
    
    return {
      id: note.id || 0,
      title: note.title || '',
      content: note.content || '',
      color: (note.color || 'yellow') as NoteColor,
      emoji: note.emoji || '📝',
      actionItems: Array.isArray(note.actionItems) ? note.actionItems : [],
      date: note.created_at?.toString() || new Date().toISOString(),
      created_at: note.created_at?.toString() || new Date().toISOString(),
      updated_at: note.updated_at?.toString() || new Date().toISOString(),
      summary: note.summary || "No summary available",  // Provide a default summary if it's missing
    };
  };
  const router = useRouter();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [notes, setNotes] = useState<DecryptedNote[]>([]);
  const [reminderTime, setReminderTime] = useState<Date | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeReminders, setActiveReminders] = useState<Map<string, number>>(new Map());
  const [editingNote, setEditingNote] = useState<number | null>(null);
  const [editTitle, setEditTitle] = useState('');
  const [editContent, setEditContent] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSummary, setSelectedSummary] = useState<{
    title: string;
    summary: string;
    color?: NoteColor;
  } | null>(null);
  const { theme, toggleTheme } = useTheme();
  const [] = useState(false);

  // Add new state
  const [reminders, setReminders] = useState<Reminder[]>([]);
  const [showReminders, setShowReminders] = useState(false);
  const [showReminderPopup, setShowReminderPopup] = useState(false);
  const [activeReminder, setActiveReminder] = useState<Reminder | null>(null);
  // const [activeUsers, setActiveUsers] = useState<User[]>([]);
  const NotificationBell = () => {
    const unreadCount = reminders.filter(r => !r.isRead).length;
    
    return (
      <div className="relative">
        <button
          onClick={() => setShowReminders(!showReminders)}
          className="relative p-2 rounded-full hover:bg-gray-100"
        >
          <Bell className={`h-6 w-6 ${unreadCount > 0 ? 'text-blue-500' : 'text-gray-500'}`} />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {unreadCount}
            </span>
          )}
        </button>
        
        {/* Reminders Dropdown */}
        {showReminders && (
          <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg z-50">
            <div className="p-4">
              <h3 className="text-lg font-semibold mb-2">Reminders</h3>
              {reminders.length === 0 ? (
                <p className="text-gray-500">No reminders</p>
              ) : (
                <div className="space-y-2">
                  {reminders.map(reminder => (
                    <div 
                      key={reminder.id}
                      className={`p-2 rounded ${reminder.isRead ? 'bg-gray-50' : 'bg-blue-50'}`}
                    >
                      <p className="font-medium">{reminder.noteTitle}</p>
                      <p className="text-sm text-gray-500">
                        {new Date(reminder.time).toLocaleString()}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Add new state for tracking shared notes
  const [sharedNoteIds, setSharedNoteIds] = useState<Set<string>>(new Set());

  // Update handleReminderSet
  const handleReminderSet = async (noteData: { title: string; content: string }, date: Date) => {
    try {
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        toast.error('Please enable notifications');
        return false;
      }
  
      const reminder: Reminder = {
        id: crypto.randomUUID(),
        noteId: Date.now(),
        noteTitle: noteData.title,
        time: date,
        isRead: false,
        content: noteData.content
      };
  
      setReminders(prev => [...prev, reminder]);
      setReminderTime(date);
  
      // Schedule notification
      const timeDiff = date.getTime() - new Date().getTime();
      setTimeout(() => {
        setActiveReminder(reminder);
        setShowReminderPopup(true);
        new Notification(reminder.noteTitle, {
          body: noteData.content,
          requireInteraction: true
        });
      }, timeDiff);
  
      return true;
    } catch (error) {
      console.error('Reminder error:', error);
      return false;
    }
  };


const deleteMeetingNote = async (noteId: number, reminderId: string) => {
  try {
    setLoading(true);
    
    // Delete from Supabase
    const { error } = await supabase
      .from('sticky_notes')
      .delete()
      .eq('id', noteId);

    if (error) throw error;

    // Update local state
    setNotes(prevNotes => prevNotes.filter(note => note.id !== noteId));
    setReminders(prevReminders => prevReminders.filter(r => r.id !== reminderId));
    
    // Clear active reminder
    const timerId = activeReminders.get(reminderId);
    if (timerId) clearTimeout(timerId);
    setActiveReminders(prev => {
      const next = new Map(prev);
      next.delete(reminderId);
      return next;
    });

    setShowReminderPopup(false);
    setActiveReminder(null);
    
    toast.success('Meeting note deleted');
  } catch (error) {
    console.error('Error deleting note:', error);
    toast.error('Failed to delete note');
  } finally {
    setLoading(false);
  }
};

const ReminderPopup = () => {
  if (!showReminderPopup || !activeReminder) return null;

  const handleDelete = async () => {
    await deleteMeetingNote(activeReminder.noteId, activeReminder.id);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
        <h3 className="text-lg font-bold mb-2">{activeReminder.noteTitle}</h3>
        <p className="mb-4">{activeReminder.content}</p>
        <div className="flex justify-end gap-2">
          <button
            onClick={() => setShowReminderPopup(false)}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Keep Note
          </button>
          <button
            onClick={handleDelete}
            disabled={loading}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
          >
            {loading ? 'Deleting...' : 'Delete Note'}
          </button>
        </div>
      </div>
    </div>
  );
};

  // Check if the user is authenticated on initial render
  useEffect(() => {
    const checkSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) throw error;
        if (!session) {
          router.push('/auth');
        }
      } catch (err) {
        console.error('Session check error:', err);
        router.push('/auth');
      }
    };

    checkSession();
  }, [router]);

  const createStickyNote = async (noteData: { title: string; content: string }) => {
    try {
      setLoading(true);

      // Validate input
      if (!noteData.title.trim() && !noteData.content.trim()) {
        toast.error('Please enter a title or content for your note');
        return;
      }

      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user?.id) {
        toast.error('Please sign in to create notes');
        throw new Error('User not authenticated');
      }

      // Get AI analysis with fallback
      let aiAnalysis;
      try {
        aiAnalysis = await summarizeNote(noteData.content);
        console.log('AI Analysis successful:', {
          hasSummary: !!aiAnalysis.summary,
          color: aiAnalysis.suggestedColor
        });
      } catch (aiError) {
        console.warn('AI analysis failed, using defaults:', aiError);
        aiAnalysis = {
          summary: noteData.content.substring(0, 100) + (noteData.content.length > 100 ? '...' : ''),
          suggestedColor: 'yellow' as const,
          mood: 'neutral',
          emoji: '📝',
          categories: [],
          priority: 1,
          actionItems: []
        };
      }

      // Encrypt note
      const encryptedNote = encryptionService.encryptNote(
        noteData.title,
        noteData.content,
        user.id
      );

      const timestamp = new Date().toISOString();
      const reminderISO = reminderTime?.toISOString() || null;

      // Combine with AI metadata
      const newNote = {
        ...encryptedNote,
        color: aiAnalysis.suggestedColor || 'yellow',
        emoji: aiAnalysis.emoji || '📝',
        mood: aiAnalysis.mood || 'neutral',
        priority: aiAnalysis.priority?.toString() || '1',
        ai_categories: aiAnalysis.categories || [],
        summary: aiAnalysis.summary || '',
        is_archived: false,
        created_at: timestamp,
        updated_at: timestamp,
        user_id: user.id,
        reminder_time: reminderTime?.toISOString() || null
      };

      // Save to database
      const { data: savedNote, error } = await supabase
        .from('sticky_notes')
        .insert([newNote])
        .select()
        .single();

      if (error) {
        toast.error('Failed to save note to database');
        throw error;
      }

      // Convert saved note to decrypted format for immediate display
      const decryptedNote: DecryptedNote = {
        id: savedNote.id,
        title: noteData.title,
        content: noteData.content,
        color: savedNote.color,
        emoji: savedNote.emoji,
        mood: savedNote.mood,
        priority: savedNote.priority,
        is_archived: savedNote.is_archived,
        created_at: savedNote.created_at,
        updated_at: savedNote.updated_at,
        ai_categories: savedNote.ai_categories,
        summary: savedNote.summary,
        user_id: savedNote.user_id,
        reminder_time: reminderISO ? new Date(reminderISO) : null,
        actionItems: false
      };

      // Update state immediately with decrypted note
      setNotes(prevNotes => [decryptedNote, ...prevNotes]);
      setTitle('');
      setContent('');
      setReminderTime(null); // Reset reminder after creating note

      toast.success('Note created successfully!');
      return decryptedNote;

    } catch (error) {
      console.error('Error creating note:', error);
      toast.error('Failed to create note. Please try again.');
    } finally {
      setLoading(false);
    }
  };

// Fetch sticky notes from Supabase
const fetchNotes = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data: encryptedNotes, error } = await supabase
      .from('sticky_notes')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false }); // Sort newest first

    if (error) throw error;

    const decryptedNotes = encryptedNotes.map((note): DecryptedNote | null => {
      try {
        const decrypted = encryptionService.decryptNote(note);
        return {
          id: note.id,
          title: decrypted.title,
          content: decrypted.content,
          color: note.color || 'yellow',
          emoji: note.emoji || '📝',
          mood: note.mood || 'neutral',
          priority: note.priority || '1',
          summary: note.summary || '',
          ai_categories: note.ai_categories || [],
          created_at: note.created_at,
          updated_at: note.updated_at,
          // Add the missing required properties
          user_id: note.user_id,
          actionItems: note.actionItems || [],
          reminder_time: note.reminder_time || null,
          is_archived: note.is_archived || false
        };
      } catch (error) {
        console.error('Failed to decrypt note:', error);
        return null;
      }
    }).filter((note): note is DecryptedNote => note !== null);
    
    setNotes(decryptedNotes);
  } catch (error) {
    console.error('Error fetching notes:', error);
  }
};

useEffect(() => {
  fetchNotes();
}, []);


  const toggleNoteSharing = async (noteId: string) => {
    try {
      setSharedNoteIds(prev => {
        const newSet = new Set(prev);
        if (newSet.has(noteId)) {
          newSet.delete(noteId);
          toast.success('Note sharing disabled');
        } else {
          newSet.add(noteId);
          toast.success('Note sharing enabled');
        }
        return newSet;
      });
    } 
    catch  {
      toast.error('Failed to toggle note sharing');
    }
  };

  const deleteStickyNote = async (id: number) => {
    setLoading(true);

    try {
      const { error: deleteError } = await supabase
        .from('sticky_notes')
        .delete()
        .eq('id', id);

      if (deleteError) throw new Error(deleteError.message);

      setNotes(notes.filter((note) => note.id !== id));
    } 
    // catch (err) {
    //   const errorMessage = (err as Error).message || 'An error occurred';
    //   console.error(errorMessage);
    // } 
    finally {
      setLoading(false);
    }
  };

  const updateStickyNote = async (noteId: number, newColor?: NoteColor) => {
      try {
        setLoading(true);
  
        const updateData: Partial<DecryptedNote> = {};
        
        if (newColor) {
          // If only updating color
          updateData.color = newColor;
        } else {
          // If updating content and title
          if (!editTitle.trim() || !editContent.trim()) {
            alert('Title and content cannot be empty');
            return;
          }
          
          const updatedSummary = await summarizeNote(editContent);
          updateData.title = editTitle.trim();
          updateData.content = editContent.trim();
          updateData.summary = typeof updatedSummary === 'string' 
            ? updatedSummary 
            : updatedSummary.summary || 'Summary unavailable';
          if (typeof updatedSummary === 'object' && updatedSummary.suggestedColor) {
            updateData.color = updatedSummary.suggestedColor;
          }
        }
  
        const { error: updateError } = await supabase
          .from('sticky_notes')
          .update(updateData)
          .eq('id', noteId);
  
        if (updateError) throw updateError;
  
        // Update local state
        setNotes(notes.map(note => 
          note.id === noteId ? { ...note, ...updateData } : note
        ));
  
        if (!newColor) {
          setEditingNote(null);
          setEditTitle('');
          setEditContent('');
        }
      } catch (error) {
        console.error('Error updating note:', error);
        alert('Failed to update note. Please try again.');
      } finally {
        setLoading(false);
      }
    };


  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/auth');
  };


  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return; // Exit if no valid destination
    
    const items = Array.from(notes);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setNotes(items);
  };

  const filteredNotes = notes.filter(note => 
    (note?.title?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false) || 
    (note?.content?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false)
  );

  

  // Adjust font size for mobile screens
  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'} 
    transition-colors duration-200 p-4`}>
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl sm:text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            🧠 StickyAI - Smart Notes
          </h1>
          <div className="flex gap-2 sm:gap-4 items-center">
            <CameraCapture 
              onCapture={(text) => {
            // <ExportDropdown notes={notes.map(convertToStickyNote)} />
                setContent(text);
              }} 
            />
           <ExportDropdown notes={notes.map(convertToStickyNote).filter(note => note !== null)} />
            
            <NotificationBell />
            <button
              onClick={toggleTheme}
              className="p-1.5 sm:p-2 rounded-full bg-gray-200 dark:bg-gray-700"
            >
              {theme === 'light' ? '🌙' : '☀️'}
            </button>
            <button
              onClick={handleSignOut}
              className="p-1.5 sm:p-2 text-sm bg-red-500 text-white rounded"
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-8">
        {/* Create Note Form - Left Side */}
        <div className="w-full sm:w-1/4">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 backdrop-blur-sm">
            <h2 className="text-xl sm:text-lg md:text-xl mb-6 font-bold text-gray-800 dark:text-white flex items-center gap-2">
              ✨ Create New Note
            </h2>
            <input
              type="text"
              placeholder="✏️ Enter note title..."
              className="p-4 mb-4 w-full border-2 border-gray-200 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm sm:text-base focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
            <textarea
              placeholder="📝 Write your note here... AI will analyze and categorize it for you!"
              className="p-4 mb-4 w-full border-2 border-gray-200 dark:border-gray-600 rounded-xl min-h-[150px] bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm sm:text-base focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200 resize-none"
              value={content}
              onChange={(e) => setContent(e.target.value)}
            />
            <div className="flex gap-4 mb-4">
              <input
                type="datetime-local"
                className="p-2 mb-2 w-full border rounded min-h-[15px] dark:bg-gray-700  text-grey-900 dark:text-white text-sm sm:text-base"
                value={reminderTime 
                  ? new Date(reminderTime.getTime() - reminderTime.getTimezoneOffset() * 60000)
                      .toISOString()
                      .slice(0, 16)
                  : ''
                }
                onChange={(e) => e.target.value && handleReminderSet({ title, content }, new Date(e.target.value))}
                min={new Date().toISOString().slice(0, 16)}
              />
              {reminderTime && (
                <button
                  onClick={() => setReminderTime(null)}
                  className="text-red-500 hover:text-red-600"
                >
                  Clear Reminder
                </button>
              )}
            </div>
            <div className="flex gap-2 mb-4">
              <VoiceInput 
                onTranscriptionComplete={async (noteData) => {
                  try {
                    await createStickyNote(noteData);
                    toast.success('Voice note created');
                  } catch {
                    toast.error('Failed to create voice note');
                  }
                }} 
              />
              {/* <CameraCapture onCapture={handleImageCapture} /> */}
            </div>
            <button
              onClick={async () => {
                try {
                  await createStickyNote({ title, content });
                } catch (error) {
                  // Error is already handled in createStickyNote
                  console.error('Button click error:', error);
                }
              }}
              className="w-full p-4 bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 text-white
              rounded-xl hover:from-purple-700 hover:via-blue-700 hover:to-indigo-700 transform hover:scale-105
              transition-all duration-300 shadow-lg hover:shadow-xl font-semibold text-lg flex items-center justify-center gap-2"
              disabled={loading}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Creating Note...
                </>
              ) : (
                <>
                  🚀 Create Note
                </>
              )}
            </button>
          </div>
        </div>

        {/* Notes Grid - Center */}
        <div className="w-full sm:w-3/4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-6">
            <div className="flex items-center gap-4 w-full sm:w-auto">
              <h2 className="text-lg sm:text-base md:text-lg font-semibold dark:text-white">Your Notes</h2>
              <input
                type="text"
                placeholder="Search notes..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 sm:flex-none p-2 border rounded w-full sm:w-64 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-sm sm:text-base"
              />
            </div>
          </div>

          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable 
              droppableId="notes" 
              direction="horizontal"
              isDropDisabled={false}
              isCombineEnabled={false}
              ignoreContainerClipping={false}
            >
              {(provided) => (
                <div 
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 overflow-hidden scrollbar-hide"
                >
                  {filteredNotes.map((note, index) => (
                    <Draggable key={note.id} draggableId={String(note.id)} index={index}>
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          className="sticky-note dark:text-gray-900 p-4 rounded-lg overflow-auto relative min-h-[300px]"
                          style={{
                            ...provided.draggableProps.style,
                            background: getColorValue(note.color || 'yellow'),
                            boxShadow: '2px 4px 8px rgba(0,0,0,0.1)',
                            display: 'inline-block',        // Makes the bubble inline so it wraps the content
                            wordWrap: 'break-word',         // Breaks long words inside the bubble
                            overflowWrap: 'break-word',     // Ensures long words do not overflow
                            padding: '8px 12px',            // Padding inside the bubble
                            // maxWidth: '80%',                // Limits the width to fit in the container
                            marginBottom: '8px',            // Adds spacing between chat bubbles
                            borderRadius: '15px',           // Rounded corners for the bubble
                            whiteSpace: 'normal',           // Ensures text wraps normally
                            boxSizing: 'border-box',        // Ensures padding is included in width
                            overflowY: 'auto',             // Prevents overflow inside the bubble

                          }}
                        >
                          {editingNote === note.id ? (
                            // Edit mode
                            <div>
                              <input
                                type="text"
                                value={editTitle}
                                onChange={(e) => setEditTitle(e.target.value)}
                                className="font-handwritten w-full mb-2 bg-transparent border-b border-gray-400 dark:text-white text-sm sm:text-base"
                              />
                              <textarea
                                value={editContent}
                                onChange={(e) => setEditContent(e.target.value)}
                                className="font-handwritten w-full mb-2 bg-transparent dark:text-white text-sm sm:text-base"
                              />
                              <div className="flex gap-2 absolute bottom-2 right-2">
                                <button
                                  onClick={() => note.id !== undefined && updateStickyNote(note.id)}
                                  className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-xs sm:text-sm"
                                >
                                  Save
                                </button>
                                <button
                                  onClick={() => setEditingNote(null)}
                                  className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 text-xs sm:text-sm"
                                >
                                  Cancel
                                </button>
                              </div>
                            </div>
                          ) : (
                            // View mode
                            <div className="relative overflow-hidden">
                              <div className="absolute top-2 right-2">
                                <Menu>
                                  <Menu.Button className="p-1.5 hover:bg-gray-100 rounded-full">
                                    <EllipsisVerticalIcon className="w-5 h-5 text-gray-500" />
                                  </Menu.Button>
                                  <Transition
                                    enter="transition duration-100 ease-out"
                                    enterFrom="transform scale-95 opacity-0"
                                    enterTo="transform scale-100 opacity-100"
                                    leave="transition duration-75 ease-in"
                                    leaveFrom="transform scale-100 opacity-100"
                                    leaveTo="transform scale-95 opacity-0"
                                  >
                                    <Menu.Items className="absolute right-0 mt-1 w-36 bg-white rounded-md shadow-lg z-50">
                                      <Menu.Item>
                                        {({ active }) => (
                                          <button
                                            onClick={() => {
                                              setEditingNote(note.id ?? null);
                                              setEditTitle(note.title);
                                              setEditContent(note.content);
                                            }}
                                            className={`${
                                              active ? 'bg-gray-100' : ''
                                            } flex items-center w-full px-3 py-2 text-sm text-gray-700`}
                                          >
                                            <PencilIcon className="w-4 h-4 mr-2" />
                                            Edit
                                          </button>
                                        )}
                                      </Menu.Item>
                                      <Menu.Item>
                                        {({ active }) => (
                                          <button
                                            onClick={() => note.id !== undefined && deleteStickyNote(note.id)}
                                            className={`${
                                              active ? 'bg-gray-100' : ''
                                            } flex items-center w-full px-3 py-2 text-sm text-red-600`}
                                          >
                                            <TrashIcon className="w-4 h-4 mr-2" />
                                            Delete
                                          </button>
                                        )}
                                      </Menu.Item>
                                      <Menu.Item>
                                        {({ active }) => (
                                          <button
                                            onClick={() => note.id && toggleNoteSharing(String(note.id))}
                                            className={`${
                                              active ? 'bg-gray-100' : ''
                                            } flex items-center w-full px-3 py-2 text-sm text-gray-700`}
                                          >
                                            <ShareIcon className="w-4 h-4 mr-2" />
                                            {sharedNoteIds.has(String(note.id)) ? 'Stop Sharing' : 'Share'}
                                          </button>
                                        )}
                                      </Menu.Item>
                                    </Menu.Items>
                                  </Transition>
                                </Menu>
                              </div>
                              
                              <div className="overflow-y-auto scrollbar-hide max-h-[calc(100vh-200px)]">
                                <div className="mb-16">
                                  <div className="flex items-center gap-2">
                                    <h3 className="font-handwritten text-xl sm:text-lg mb-3 text-gray-900">
                                      {note.title}
                                    </h3>
                                    <span className="text-2xl">{note.emoji}</span>
                                  </div>
                                  
                                  {/* Mood and Categories */}
                                  <div className="flex gap-2 mb-3">
                                    {note.mood && (
                                      <span className="text-xs bg-white/50 px-2 py-1 rounded">
                                        Mood: {note.mood}
                                      </span>
                                    )}
                                    {note.ai_categories?.map((category: string, index: number) => (
                                     <span key={`${category}-${index}`} className="text-xs bg-white/50 px-2 py-1 rounded">
                                         #{category}
                                     </span>
                                    ))}
                                  </div>

                                  {/* Priority Indicator */}
                                  {note.priority && (
                                    <div className="mb-3">
                                      <span className="text-xs font-semibold">
                                        Priority: {'⭐'.repeat(note.priority)}
                                      </span>
                                    </div>
                                  )}

                                  <p className="font-handwritten text-gray-900 mb-3 whitespace-pre-wrap text-sm sm:text-base">
                                    {note.content}
                                  </p>

                                  {/* Action Items */}
                                  {Array.isArray(note.actionItems) && note.actionItems.length > 0 && (
                                    <div className="mt-2 bg-white/50 p-2 rounded">
                                      <p className="text-xs font-medium mb-1">Action Items:</p>
                                      <ul className="list-disc list-inside">
                                        {note.actionItems.map((item, index: number) => (
                                          <li key={index} className="text-sm flex items-center gap-2">
                                            <span>{item.task}</span>
                                            {item.dueDate && (
                                              <span className="text-xs bg-white/50 px-1 rounded">
                                                Due: {new Date(item.dueDate).toLocaleDateString()}
                                              </span>
                                            )}
                                            <span className={`text-xs ${
                                              item.priority === 'high' ? 'text-red-500' :
                                              item.priority === 'medium' ? 'text-yellow-500' :
                                              'text-green-500'
                                            }`}>
                                              {item.priority}
                                            </span>
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  )}

                                  {/* Existing Summary Section */}
                                  <div 
                                    onClick={() => setSelectedSummary({
                                      title: note.title,
                                      summary: note.summary || 'No summary available',
                                      color: note.color as NoteColor
                                    })}
                                    className="mt-2 p-2 bg-white/50 rounded cursor-pointer hover:bg-white/60 transition-colors"
                                  >
                                    <p className="text-xs font-medium mb-1">Summary:</p>
                                    <p className="text-sm line-clamp-2 font-handwritten">
                                      {note.summary || 'No summary available'}
                                    </p>
                                    <p className="text-xs text-gray-600 mt-1">Click to view full summary</p>
                                  </div>
                                </div>

                                <div className="flex gap-2 absolute bottom-2 left-2">
                                  {['yellow', 'blue', 'green', 'pink', 'purple'].map((color) => (
                                    <button
                                      key={color}
                                      onClick={() => note.id !== undefined && updateStickyNote(note.id, color as NoteColor)}
                                      className={`w-6 h-6 rounded-full border-2 ${note.color === color ? 'border-black' : 'border-transparent'}`}
                                      style={{ background: getColorValue(color as NoteColor) }}
                                    />
                                  ))}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </div>
      </div>

      {selectedSummary && (
        <SummaryModal
          isOpen={!!selectedSummary}
          onClose={() => setSelectedSummary(null)}
          title={selectedSummary.title}
          summary={selectedSummary.summary}
          color={selectedSummary.color}
        />
      )}
      <ReminderPopup />
    </div>
  );
};

export default HomePage;
