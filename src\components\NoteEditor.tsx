'use client';

import { useState } from 'react';
import { Upload } from 'lucide-react';
import { summarizeWithGroq, analyzeSentiment, generateTagsFromContent } from '../../groqAi';
import Image from 'next/image';

export default function NoteEditor() {
  const [images, setImages] = useState<string[]>([]);
  const [content, setContent] = useState<string>('');

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    // For now, we'll just create URL previews
    // In production, you'd want to upload to a storage service
    const imageUrls = Array.from(files).map(file => URL.createObjectURL(file));
    setImages(prev => [...prev, ...imageUrls]);
  };

  const handleAnalyzeNote = async (content: string) => {
    try {
      const summary = await summarizeWithGroq(content);
      const sentiment = await analyzeSentiment(content);
      const tags = await generateTagsFromContent(content);
      
      console.log({ summary, sentiment, tags });
    } catch (error) {
      console.error('Error analyzing note:', error);
    }
  };

  return (
    <div className="space-y-4">
      <textarea
        value={content}
        onChange={(e) => setContent(e.target.value)}
        className="w-full p-2 border rounded"
        placeholder="Write your note here..."
      />
      
      {/* Image upload button */}
      <div className="flex items-center gap-2">
        <button className="flex items-center gap-2 px-3 py-2 border rounded">
          <Upload className="h-4 w-4" />
          Add Image
          <input
            type="file"
            accept="image/*"
            multiple
            className="hidden"
            onChange={handleImageUpload}
          />
        </button>
      </div>

      {/* Image previews */}
      <div className="flex flex-wrap gap-2">
        {images.map((url, index) => (
          <Image 
            key={index}
            src={url}
            alt={`Upload ${index + 1}`}
            width={96}
            height={96}
            className="object-cover rounded"
          />
        ))}
      </div>

      <button onClick={() => handleAnalyzeNote(content)}>
        Analyze Note
      </button>
    </div>
  );
} 