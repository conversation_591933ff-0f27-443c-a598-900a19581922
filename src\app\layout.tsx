// 'use client';
  
import './globals.css';
import { Metadata } from 'next';
import Providers from '@/components/Providers'; // Custom providers component
import { ThemeProvider } from '@/context/ThemeContext'; // Your custom theme context provider

export const metadata: Metadata = {
  title: 'StickyAI',
  description: 'Your AI-powered sticky notes app',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        {/* Meta tags, link to fonts or external resources can go here */}
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {/* More meta tags and link to favicon */}
      </head>
      <body>
        {/* ThemeProvider is wrapping the entire app for theme context */}
        <ThemeProvider>
          {/* Providers wraps your app with additional context providers */}
          <Providers>
            {/* This renders the main content of the page */}
            {children}
          </Providers>
        </ThemeProvider>
      </body>
    </html>
  );
}
