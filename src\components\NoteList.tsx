// 'use client';

// import Note from '../../src/components/Note';
// import { useRouter } from 'next/router';

// interface Note {
//   id: string;
//   title: string;
//   content: string;
// }

// interface NoteListProps {
//   notes: Note[];
// }

// export const NoteList = ({ notes }: NoteListProps) => {
//   const router = useRouter();

//   const handleNoteClick = (note: Note) => {
//     // console.log('Note clicked:', note);
//     router.push(`/notes/${note.id}`);
//   };

//   return (
//     <div>
//       {notes.map((note: Note) => (
//         <Note 
//           key={note.id} 
//           title={note.title} 
//           content={note.content} 
//           onClick={() => handleNoteClick(note)} 
//         />
//       ))}
//     </div>
//   );
// } 