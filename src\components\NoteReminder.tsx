// import { useState } from 'react';
// import { supabase } from '../../supabase';

// interface ReminderProps {
//   noteId: string;
//   userId: string;
// }

// export const NoteReminder = ({ noteId, userId }: ReminderProps) => {
//   const [reminderDate, setReminderDate] = useState('');
  
//   const setReminder = async () => {
//     try {
//       const { error } = await supabase
//         .from('reminders')
//         .insert({
//           note_id: noteId,
//           user_id: userId,
//           reminder_date: new Date(reminderDate),
//           is_completed: false,
//         });

//       if (error) throw error;
      
//       // You can use Supabase Edge Functions for notifications
//       // or integrate with a third-party service like Novu
//     } catch (error) {
//       console.error('Error setting reminder:', error);
//     }
//   };

//   return (
//     <div className="flex items-center gap-2">
//       <input
//         type="datetime-local"
//         value={reminderDate}
//         onChange={(e) => setReminderDate(e.target.value)}
//         className="rounded border p-1"
//       />
//       <button
//         onClick={setReminder}
//         className="bg-purple-500 text-white px-3 py-1 rounded"
//       >
//         Set Reminder
//       </button>
//     </div>
//   );
// }; 