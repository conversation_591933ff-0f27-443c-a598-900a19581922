// // src/pages/api/createNote.ts

// import type { NextApiRequest, NextApiResponse } from 'next';
// import { createClient } from '@supabase/supabase-js';
// import { summarizeNote } from '../../../groqAi'; // Adjust path to your groqAi file
// import { encryptionService } from '../../utils/encryption'; // Adjust path

// // Initialize Supabase Admin Client for server-side operations
// // Use the SERVICE_ROLE_KEY for admin privileges. Store it in .env.local
// const supabaseAdmin = createClient(
//   process.env.NEXT_PUBLIC_SUPABASE_URL!,
//   process.env.SUPABASE_SERVICE_KEY!
// );

// export default async function handler(req: NextApiRequest, res: NextApiResponse) {
//   if (req.method !== 'POST') {
//     return res.status(405).json({ error: 'Method not allowed' });
//   }

//   try {
//     const { title, content, reminderTime } = req.body;
//     const { data: { user } } = await supabaseAdmin.auth.getUser(req.headers.authorization!);

//     if (!user) {
//       return res.status(401).json({ error: 'User not authenticated' });
//     }

//     // 1. Get AI analysis on the server
//     const aiAnalysis = await summarizeNote(content);

//     // 2. Encrypt note securely on the server
//     const encryptedNote = encryptionService.encryptNote(
//       title,
//       content
//       , // The encryption service should use process.env.ENCRYPTION_MASTER_KEY internally,
//       user.id
//     );

//     const timestamp = new Date().toISOString();

//     const newNote = {
//       ...encryptedNote,
//       user_id: user.id,
//       color: aiAnalysis.suggestedColor || 'yellow',
//       emoji: aiAnalysis.emoji || '📝',
//       mood: aiAnalysis.mood || 'neutral',
//       priority: aiAnalysis.priority?.toString() || '1',
//       ai_categories: aiAnalysis.categories || [],
//       summary: aiAnalysis.summary || '',
//       is_archived: false,
//       created_at: timestamp,
//       updated_at: timestamp,
//       reminder_time: reminderTime || null,
//     };

//     // 3. Save to database
//     const { data: savedNote, error } = await supabaseAdmin
//       .from('sticky_notes')
//       .insert([newNote])
//       .select()
//       .single();

//     if (error) {
//       console.error('Supabase insert error:', error);
//       throw new Error(error.message);
//     }
    
//     // 4. Send back the newly created (but decrypted) note for immediate UI update
//     const decryptedNoteForClient = {
//         id: savedNote.id,
//         title: title,
//         content: content,
//         ...savedNote // include other fields like color, emoji, etc.
//     };

//     res.status(200).json(decryptedNoteForClient);

//   } catch (error: any) {
//     console.error('API Error in /api/createNote:', error);
//     res.status(500).json({ error: 'Failed to process note on server', details: error.message });
//   }
// }