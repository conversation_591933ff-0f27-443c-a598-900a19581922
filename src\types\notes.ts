export interface ActionItem {
  id: number;
  text: string;
  completed: boolean;
}

export type NoteColor = 'yellow' | 'blue' | 'green' | 'pink';

export interface BaseNote {
  id: any;
  title: string;
  content: string;
  color: NoteColor;
  emoji: string;
  mood: string;
  priority: number;
  is_archived: boolean;
  updated_at: string | null;
}

export interface StickyNote extends TimeStamps {
  summary: string;
  id: number;
  title: string;
  content: string;
  color: NoteColor;
  emoji: string;
  actionItems: any[];
  date: string;
}

export interface EncryptedNote {
  id: number;
  encrypted_title: Buffer;
  encrypted_content: Buffer;
  salt: Buffer;
  user_id: string;
  color?: string;
  emoji?: string;
  created_at?: string;
}

export interface DecryptedNote {
  id: number;
  title: string;
  content: string;
  color: NoteColor;
  emoji: string;
  mood: string;
  priority: string;
  summary: string;
  ai_categories: any[];
  created_at: string;
  updated_at: string | null;
}

export interface TimeStamps {
  created_at: string;
  updated_at: string;
} 
