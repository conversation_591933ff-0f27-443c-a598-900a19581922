// 'use client';

// import {
//     DndContext,
//     closestCenter,
//     KeyboardSensor,
//     PointerSensor,
//     useSensor,
//     useSensors,
//     DragEndEvent
//   } from '@dnd-kit/core';
//   import {
//     arrayMove,
//     SortableContext,
//     horizontalListSortingStrategy,
//     useSortable
//   } from '@dnd-kit/sortable';
  
//   // Define your Note interface
//   interface Note {
//     id: string;
//     content: string;
//   }
  
//   // Define the StickyNoteBoardProps interface
//   interface StickyNoteBoardProps {
//     notes: Note[];
//     setNotes: React.Dispatch<React.SetStateAction<Note[]>>;
//     isDropDisabled?: boolean; // Optional boolean to control drop functionality
//   }
  
//   // StickyNote component to handle individual note rendering and sorting
//   function StickyNote({ note }: { note: Note }) {
//     const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
//       id: note.id
//     });
  
//     // Styling for dragging effects
//     const style = {
//       transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
//       transition
//     };
  
//     return (
//       <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
//         <div className="sticky-note">
//           {note.content}
//         </div>
//       </div>
//     );
//   }
  
//   // StickyNoteBoard component to manage the drag-and-drop context
//   function StickyNoteBoard({ notes, setNotes, isDropDisabled = false }: StickyNoteBoardProps) {
//     // Sensors to detect drag behavior
//     const sensors = useSensors(
//       useSensor(PointerSensor),
//       useSensor(KeyboardSensor)
//     );
  
//     // Handle the end of a drag event
//     const handleDragEnd = (event: DragEndEvent) => {
//       const { active, over } = event;
  
//       // If there's no valid drop or drop is disabled, return early
//       if (!over || isDropDisabled) return;
  
//       // Perform the reordering only if the active and over items are different
//       if (active.id !== over.id) {
//         setNotes((items) => {
//           const oldIndex = items.findIndex((item) => item.id === active.id);
//           const newIndex = items.findIndex((item) => item.id === over.id);
//           return arrayMove(items, oldIndex, newIndex);
//         });
//       }
//     };
  
//     return (
//       <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
//         <SortableContext items={notes.map(note => note.id)} strategy={horizontalListSortingStrategy}>
//           <div className="sticky-board">
//             {notes.map((note) => (
//               <StickyNote key={note.id} note={note} />
//             ))}
//           </div>
//         </SortableContext>
//       </DndContext>
//     );
//   }
  
//   export default StickyNoteBoard;
  