import React, { useEffect, useState, useCallback, useRef } from 'react';
import { supabase } from '../../supabase';
import { toast } from 'react-hot-toast';
import { Users } from 'lucide-react';

interface CollaborativeNoteProps {
  noteId: string;
  content: string;
  onContentChange: (content: string) => void;
  currentUser?: {
    id: string;
    email?: string;
    avatar_url?: string;
  };
}

interface Collaborator {
  id: string;
  email?: string;
  lastActive: number;
  color: string;
}

const COLORS = [
  'bg-blue-500',
  'bg-green-500',
  'bg-purple-500',
  'bg-pink-500',
  'bg-yellow-500',
  'bg-red-500',
];

export const CollaborativeNote: React.FC<CollaborativeNoteProps> = ({
  noteId,
  content,
  onContentChange,
  currentUser,
}) => {
  const [collaborators, setCollaborators] = useState<Map<string, Collaborator>>(new Map());
  const channelRef = useRef<any>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout>();

  // Debounced content update to reduce broadcast frequency
  const debouncedBroadcast = useCallback((newContent: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    debounceTimerRef.current = setTimeout(() => {
      channelRef.current?.send({
        type: 'broadcast',
        event: 'content-update',
        payload: { content: newContent }
      });
    }, 100);
  }, []);

  // Set up real-time collaboration
  useEffect(() => {
    const channel = supabase
      .channel(`note-${noteId}`)
      .on('presence', { event: 'sync' }, () => {
        try {
          const presenceState = channel.presenceState();
          const newCollaborators = new Map<string, Collaborator>();
          
          Object.entries(presenceState).forEach(([userId, userStates]: [string, any[]]) => {
            const userState = userStates[0];
            newCollaborators.set(userId, {
              id: userId,
              email: userState.email,
              lastActive: Date.now(),
              color: COLORS[newCollaborators.size % COLORS.length],
            });
          });
          
          setCollaborators(newCollaborators);
        } catch (error) {
          console.error('Error processing presence state:', error);
        }
      })
      .on('broadcast', { event: 'content-update' }, ({ payload }) => {
        if (payload.content !== content) {
          onContentChange(payload.content);
        }
      })
      .subscribe(async (status) => {
        if (status !== 'SUBSCRIBED') {
          toast.error('Failed to connect to collaboration server');
          return;
        }

        // Enter the channel with user info
        if (currentUser) {
          await channel.track({
            user_id: currentUser.id,
            email: currentUser.email,
            online_at: new Date().toISOString(),
          });
        }
      });

    channelRef.current = channel;

    // Cleanup subscription
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      channel.unsubscribe();
    };
  }, [noteId, currentUser, content, onContentChange]);

  return (
    <div className="relative space-y-4">
      {/* Collaborators Display */}
      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
        <Users className="w-4 h-4 text-gray-500" />
        <div className="flex -space-x-2">
          {Array.from(collaborators.values()).map(collaborator => (
            <div
              key={collaborator.id}
              className={`w-8 h-8 rounded-full ${collaborator.color} flex items-center 
                         justify-center text-white text-sm border-2 border-white
                         hover:z-10 transition-transform hover:scale-110`}
              title={collaborator.email || collaborator.id}
            >
              {(collaborator.email || collaborator.id)[0].toUpperCase()}
            </div>
          ))}
        </div>
        <span className="text-sm text-gray-500 ml-2">
          {collaborators.size} {collaborators.size === 1 ? 'person' : 'people'} collaborating
        </span>
      </div>

      {/* Collaborative Text Area */}
      <textarea
        value={content}
        onChange={(e) => {
          const newContent = e.target.value;
          onContentChange(newContent);
          debouncedBroadcast(newContent);
        }}
        className="w-full min-h-[200px] p-4 rounded border focus:ring-2 
                   focus:ring-blue-500 focus:border-transparent outline-none
                   resize-y"
        placeholder="Start typing to collaborate..."
      />
    </div>
  );
};

export default CollaborativeNote;