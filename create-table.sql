-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Main sticky_notes table with all features
CREATE TABLE IF NOT EXISTS sticky_notes (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    -- Encrypted fields
    encrypted_title TEXT NOT NULL,
    encrypted_content TEXT NOT NULL,
    -- Metadata fields
    color TEXT DEFAULT 'yellow',
    emoji TEXT DEFAULT '📝',
    mood TEXT DEFAULT 'neutral',
    priority TEXT DEFAULT '1',
    ai_categories TEXT[] DEFAULT ARRAY[]::TEXT[],
    summary TEXT,
    is_archived BOOLEAN DEFAULT false,
    reminder_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS
ALTER TABLE sticky_notes ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own notes" ON sticky_notes;
DROP POLICY IF EXISTS "Users can insert their own notes" ON sticky_notes;
DROP POLICY IF EXISTS "Users can update their own notes" ON sticky_notes;
DROP POLICY IF EXISTS "Users can delete their own notes" ON sticky_notes;

-- Create policies
CREATE POLICY "Users can view their own notes"
    ON sticky_notes FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own notes"
    ON sticky_notes FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own notes"
    ON sticky_notes FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notes"
    ON sticky_notes FOR DELETE
    USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notes_user_id ON sticky_notes(user_id);
CREATE INDEX IF NOT EXISTS idx_notes_created_at ON sticky_notes(created_at);
