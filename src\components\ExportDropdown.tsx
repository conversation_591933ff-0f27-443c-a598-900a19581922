import { ArrowUpTrayIcon } from "@heroicons/react/16/solid";
import { jsPDF } from "jspdf";

// Define the Note type to ensure type safety for the notes prop
interface Note {
  title: string;
  content: string;
  summary: string;
}

// Define the ExportDropdownProps interface
interface ExportDropdownProps {
  notes: Note[]; // Notes is an array of Note objects
}

// Modify the ExportDropdown component
export const ExportDropdown: React.FC<ExportDropdownProps> = ({ notes }) => {
  const exportToPDF = () => {
    const doc = new jsPDF();

    // Loop through the notes to add each note's content to the PDF
    notes.forEach((note, index) => {
      doc.text(`Title: ${note.title}`, 10, 10 + index * 40);
      doc.text(`Content: ${note.content}`, 10, 20 + index * 40);
      doc.text(`Summary: ${note.summary}`, 10, 30 + index * 40);

      // Add a new page for each note except the last one
      if (index < notes.length - 1) doc.addPage();
    });

    // Save the generated PDF with a specific name
    doc.save('my-sticky-notes.pdf');
  };

  return (
    <button 
      onClick={exportToPDF}
      className="p-1.5 sm:p-2 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
    >
      <ArrowUpTrayIcon className="h-5 w-5" />
    </button>
  );
};
