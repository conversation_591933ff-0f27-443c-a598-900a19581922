// app/auth/callback/route.ts (or `pages/api/auth/callback.ts` if using pages directory)
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const origin = requestUrl.origin

  // Check if the 'code' query parameter is present in the URL
  if (!code) {
    return NextResponse.redirect(`${origin}/auth?error=missing_code`)
  }

  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Exchange the code for a session
    const { data, error } = await supabase.auth.exchangeCodeForSession(code)

    // Handle error from Supabase OAuth callback
    if (error) {
      console.error('Error exchanging code for session:', error.message)
      return NextResponse.redirect(`${origin}/auth?error=auth_error`)
    }

    // Successful authentication, redirect to home or dashboard instead of /auth
    console.log('Authentication successful:', data)
    return NextResponse.redirect(`${origin}/dashboard`)
  } catch (error) {
    console.error('Unexpected error during auth callback:', error)
    return NextResponse.redirect(`${origin}/auth?error=callback_error`)
  }
}
