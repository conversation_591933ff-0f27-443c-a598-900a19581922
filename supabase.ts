// src/utils/supabase.ts
import { createClient } from '@supabase/supabase-js'

// Client-side Supabase setup
export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,  // Your Supabase URL from .env.local
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
   // Your Supabase anon key from .env.local
  {
    auth: {
      persistSession: true,  // Persist user session in local storage
      autoRefreshToken: true,  // Automatically refresh token when expired
      detectSessionInUrl: true,  // Detect session in the URL (good for OAuth)
    }
  }
)

export { createClient }
