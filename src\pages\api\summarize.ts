import { summarizeNote } from '../../../groqAi';
import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { content } = req.body;
    if (!content) {
      return res.status(400).json({ error: 'Content is required' });
    }

    const analysis = await summarizeNote(content);
    return res.status(200).json(analysis);

  } catch (error) {
    console.error('Summary API error:', error);
    return res.status(500).json({ error: 'Failed to generate summary' });
  }
}