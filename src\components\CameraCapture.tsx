'use client';
import { useState } from 'react';
import Tesseract from 'tesseract.js';
import { Dialog } from '@headlessui/react';
import { CameraIcon } from 'lucide-react';
// Add this new component before HomePage
export const CameraCapture: React.FC<{
    onCapture: (text: string) => void;
  }> = ({ onCapture }) => {
    const [showCamera, setShowCamera] = useState(false);
    const [processing, setProcessing] = useState(false);
    
    const handleImageCapture = async (file: File) => {
      setProcessing(true);
      try {
        // Use Tesseract to extract text from image
        const { data: { text } } = await Tesseract.recognize(file, 'eng');
        onCapture(text);
      } catch (error) {
        console.error('OCR Error:', error);
        alert('Failed to process image. Please try again.');
      }
      setProcessing(false);
      setShowCamera(false);
    };
  
    return (
      <>
        <button
          onClick={() => setShowCamera(true)}
          className="p-1.5 sm:p-2 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
        >
          <CameraIcon className="h-5 w-5" />
        </button>
  
        {showCamera && (
          <Dialog
            open={showCamera}
            onClose={() => setShowCamera(false)}
            className="relative z-50"
          >
            <div className="fixed inset-0 bg-black/30" />
            <div className="fixed inset-0 flex items-center justify-center p-4">
              <Dialog.Panel className="bg-white dark:bg-gray-800 rounded-lg p-4">
                <input
                  type="file"
                  accept="image/*"
                  capture="environment"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleImageCapture(file);
                  }}
                  className="hidden"
                  id="camera-input"
                />
                <label
                  htmlFor="camera-input"
                  className="cursor-pointer block p-4 text-center"
                >
                  {processing ? (
                    <p>Processing image...</p>
                  ) : (
                    <p>Click to take photo of sticky note</p>
                  )}
                </label>
              </Dialog.Panel>
            </div>
          </Dialog>
        )}
      </>
    );
  };