import React, { useState } from 'react';

const validatePassword = (pass: string): boolean => {
  return pass.length >= 12 && /[A-Za-z]/.test(pass) && /[0-9]/.test(pass);
}

// Create a React component to hold the state and handlers
const AuthForm: React.FC = () => {
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');  // Added error state

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validatePassword(password)) {
      setError('Password must be at least 12 characters with letters and numbers');
      return;
    }
    
    // proceed with signup/signin
  }; 

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="password"
        value={password}
        onChange={handlePasswordChange}
        placeholder="Enter password"
      />
      {error && <div className="error">{error}</div>}
    </form>
  );
};

export default AuthForm;
