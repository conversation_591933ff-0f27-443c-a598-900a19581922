import { createClient } from '@supabase/supabase-js';
import { encryptionService } from '../../utils/encryption';

export class NoteService {
  private supabase;

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
  }

  async fetchNotes(userId: string) {
    try {
      const { data: notes, error } = await this.supabase
        .from('sticky_notes')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return notes.map(note => ({
        ...note,
        decrypted: encryptionService.decryptNote(note)
      }));
    } catch (error) {
      console.error('Error fetching notes:', error);
      throw error;
    }
  }

  async createNote(noteData: any) {
    try {
      const { data, error } = await this.supabase
        .from('sticky_notes')
        .insert([noteData])
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating note:', error);
      throw error;
    }
  }
}

export const noteService = new NoteService();