export const sensitiveDataPatterns = {
  // South African patterns
  idNumber: /\b\d{13}\b/g,
  passportNumber: /\b[A-Z]\d{8}\b/g,
  phoneNumber: /\b(?:\+27|0)[0-9]{9}\b/g,
  bankAccount: /\b\d{5,16}\b/g,
  email: /\b[\w\.-]+@[\w\.-]+\.\w+\b/g,
  creditCard: /\b(?:\d[ -]*?){13,16}\b/g,
  taxNumber: /\b\d{10}\b/g
};

export const maskSensitiveData = (text: string): string => {
  let maskedText = text;
  Object.entries(sensitiveDataPatterns).forEach(([, pattern]) => {
    maskedText = maskedText.replace(pattern, match => '*'.repeat(match.length));
  });
  return maskedText;
};