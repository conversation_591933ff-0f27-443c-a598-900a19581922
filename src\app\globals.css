@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('https://fonts.googleapis.com/css2?family=Indie+Flower&display=swap');

/* Custom styles */
body {
    font-family: 'Arial', sans-serif;
    background-color: #f0f0f0;
    color: #333;
    margin: 0;
    padding: 0;
}

h1, h2, h3 {
    color: #2c3e50;
}

a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Sticky Note Styles */
.font-handwritten {
    font-family: 'Indie Flower', cursive;
}

.sticky-note {
  @apply p-6 rounded-lg relative min-h-[200px] transition-all duration-200;
}

.sticky-note:hover {
  transform: rotate(-1deg) scale(1.02);
}

.sticky-note::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  border-width: 0 16px 16px 0;
  border-style: solid;
  border-color: rgba(0,0,0,0.1) #f0f0f0;
}

/* Add any additional dark mode specific styles here */

/* Remove scrollbars and limit text overflow */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
@font-face {
  font-family: 'Handwritten';
  /* Add your preferred handwritten font source */
}

.font-handwritten {
  font-family: 'Indie Flower', cursive;
}