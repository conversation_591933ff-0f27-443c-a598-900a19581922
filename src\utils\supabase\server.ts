// import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
// import crypto from 'crypto';

// // Function to create Supabase client (server-side)
// export const createClient = (cookies: any) => {
//   return createServerComponentClient({ cookies });
// };

// // Encryption function
// export const encryptNote = (
//   title: string,
//   content: string,
//   userId: string
// ): { encryptedTitle: Buffer; encryptedContent: Buffer } => {
//   const masterKey = process.env.ENCRYPTION_MASTER_KEY; // Use environment variable for the master key
//   if (!masterKey) {
//     throw new Error("Encryption master key is not set.");
//   }

//   if (!userId) {
//     throw new Error("User ID must be provided.");
//   }

//   // Create a user-specific key by hashing the master key and user ID
//   const userKey = crypto
//     .createHmac('sha256', masterKey)
//     .update(userId)
//     .digest();

//   // Encryption function using AES-256-GCM
//   const encrypt = (data: string): Buffer => {
//     const iv = crypto.randomBytes(12); // Random initialization vector
//     const cipher = crypto.createCipheriv('aes-256-gcm', userKey, iv);

//     // Encrypt data and append authentication tag
//     const encrypted = Buffer.concat([cipher.update(data, 'utf8'), cipher.final()]);
//     const tag = cipher.getAuthTag(); // Authentication tag for AES-GCM

//     return Buffer.concat([iv, encrypted, tag]); // Concatenate IV, encrypted data, and tag
//   };

//   // Encrypt the title and content
//   return {
//     encryptedTitle: encrypt(title),
//     encryptedContent: encrypt(content),
//   };
// };

// // Decryption function
// export const decryptNote = (
//   note: { encrypted_title: Buffer; encrypted_content: Buffer },
//   userId: string
// ): { title: string; content: string } => {
//   const masterKey = process.env.ENCRYPTION_MASTER_KEY; // Use environment variable for the master key
//   if (!masterKey) throw new Error("Encryption master key is not set.");

//   // Create user-specific key
//   const userKey = crypto
//     .createHmac('sha256', masterKey)
//     .update(userId)
//     .digest();

//   // Decrypt function using AES-256-GCM
//   const decrypt = (encryptedData: Buffer): string => {
//     // Extract IV, encrypted content, and authentication tag
//     const iv = encryptedData.subarray(0, 12);
//     const tag = encryptedData.subarray(-16);
//     const content = encryptedData.subarray(12, -16);

//     const decipher = crypto.createDecipheriv('aes-256-gcm', userKey, iv);
//     decipher.setAuthTag(tag); // Set the authentication tag

//     try {
//       return Buffer.concat([decipher.update(content), decipher.final()]).toString('utf8');
//     } catch (error) {
//       console.error('Decryption error:', error);
//       throw new Error("Failed to decrypt note");
//     }
//   };

//   // Decrypt the title and content
//   return {
//     title: decrypt(note.encrypted_title),
//     content: decrypt(note.encrypted_content),
//   };
// };

// // Function to convert encrypted data to base64 for storage in the database
// export const encryptNoteForStorage = (
//   title: string,
//   content: string,
//   userId: string
// ) => {
//   const { encryptedTitle, encryptedContent } = encryptNote(title, content, userId);

//   // Convert Buffer to base64 strings for database storage
//   const encryptedTitleBase64 = encryptedTitle.toString('base64');
//   const encryptedContentBase64 = encryptedContent.toString('base64');

//   return {
//     encryptedTitleBase64,
//     encryptedContentBase64,
//   };
// };

// // Function to convert base64 data back to Buffer for decryption
// export const decryptNoteFromStorage = (
//   encryptedTitleBase64: string,
//   encryptedContentBase64: string,
//   userId: string
// ) => {
//   // Convert base64 strings back to Buffers
//   const encryptedTitleBuffer = Buffer.from(encryptedTitleBase64, 'base64');
//   const encryptedContentBuffer = Buffer.from(encryptedContentBase64, 'base64');

//   // Decrypt the note
//   return decryptNote(
//     { encrypted_title: encryptedTitleBuffer, encrypted_content: encryptedContentBuffer },
//     userId
//   );
// };
