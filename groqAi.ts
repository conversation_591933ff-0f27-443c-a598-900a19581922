'use client';

import axios from 'axios';

const groqAPIUrl = 'https://api.groq.com/openai/v1/chat/completions';  // Correct endpoint
const apiKey = process.env.NEXT_PUBLIC_GROQ_API_KEY;  // Make it accessible on client side
if (!apiKey) {
  throw new Error('GROQ API key is not configured');
}

const standardHeaders = {
  'Authorization': `Bearer ${apiKey}`,
  'Content-Type': 'application/json',
};

export interface AIResponse {
  summary: string;
  suggestedColor: 'yellow' | 'blue' | 'green' | 'pink' | 'purple';
  mood: string;
  emoji: string;
  categories: string[];
  priority: number;
  suggestedReminder?: string;
  actionItems: {
    task: string;
    dueDate?: string;
    priority: 'high' | 'medium' | 'low';
  }[];
}

// Simple input sanitization
const sanitizeInput = (content: string): string => {
  // Remove any potentially dangerous characters or patterns
  return content.replace(/<[^>]*>?/gm, '').trim();
};

// Simple sensitive data masking
const maskSensitiveData = (content: string): string => {
  // Basic masking for common sensitive patterns
  return content
    .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '***-**-****') // SSN
    .replace(/\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g, '****-****-****-****') // Credit card
    .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '***@***.***') // Email
    .replace(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, '***-***-****'); // Phone
};

// Generate unique request ID
const generateRequestId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// Axios config with timeout and headers
const axiosConfig = {
  headers: {
    ...standardHeaders,
    'X-Request-ID': generateRequestId(),
  },
  timeout: 15000, // 15 second timeout
};

// Function to call Groq's summarization API
export const summarizeWithGroq = async (content: string) => {
  try {
    const response = await axios.post(
      groqAPIUrl,
      {
        messages: [
          {
            role: "system",
            content: "You are a friendly assistant that provides short, clear, and concise summaries."
          },
          {
            role: "user",
            content: `Please summarize this text in a short and concise manner: ${content}`
          }
        ],
        model: "llama3-8b-8192",  // or "llama2-70b-4096"
        temperature: 0.5,
        max_tokens: 100,  // Limit the length of the response
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
      }
    );

    // Ensure the API response contains the summary message
    if (response.data.choices && response.data.choices[0].message.content) {
      const summary = response.data.choices[0].message.content;
      
      // Return only the concise summary without extra text
      return summary;
    } else {
      console.error('No summary returned from Groq API:', response.data);
      return 'Summary unavailable'; // Fallback if no summary is returned
    }
  } catch (error) {
    console.error('Error with Groq AI summarization:', error);
    throw new Error('Failed to summarize the content with Groq');
  }
};

// Sentiment Analysis (Mood Detection)
export const analyzeSentiment = async (content: string) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        { role: "system", content: "You are an assistant that analyzes the mood of the content." },
        { role: "user", content: `Analyze the sentiment of this text: ${content}` }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 100,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content;
    } else {
      console.error('No sentiment returned from Groq API:', response.data);
      return 'Neutral';  // Default sentiment if none returned
    }
  } catch (error) {
    console.error('Error with sentiment analysis:', error);
    return 'Error analyzing sentiment';
  }
};

// Context-Based Suggestions (Smart Suggestions)
export const suggestActionsBasedOnContent = async (content: string) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        { role: "system", content: "You are an assistant that provides smart suggestions based on note content." },
        { role: "user", content: `Based on this note, what actions or suggestions can you provide? Content: ${content}` }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 150,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content;
    } else {
      console.error('No suggestions returned from Groq API:', response.data);
      return 'No suggestions available';
    }
  } catch (error) {
    console.error('Error with action suggestions:', error);
    return 'Error generating suggestions';
  }
};

// Tagging and Organization (AI-Generated Tags)
export const generateTagsFromContent = async (content: string) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        { role: "system", content: "You are an assistant that generates tags based on note content." },
        { role: "user", content: `Please generate tags for this note: ${content}` }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 100,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content.split(',');  // Return tags as an array
    } else {
      console.error('No tags returned from Groq API:', response.data);
      return ['Uncategorized'];
    }
  } catch (error) {
    console.error('Error generating tags:', error);
    return ['Error generating tags'];
  }
};

// Actionable To-Do Items (Task Extraction)
export const extractTasksFromNote = async (content: string) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        { role: "system", content: "You are an assistant that extracts tasks from note content." },
        { role: "user", content: `Please extract tasks from this note: ${content}` }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 150,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content.split(',');  // Return tasks as an array
    } else {
      console.error('No tasks extracted from Groq API:', response.data);
      return ['No tasks identified'];
    }
  } catch (error) {
    console.error('Error extracting tasks:', error);
    return ['Error extracting tasks'];
  }
};

// Quick Rewrites or Improvements (Enhance Writing)
export const rephraseText = async (content: string) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        { role: "system", content: "You are an assistant that helps improve the writing." },
        { role: "user", content: `Please rephrase the following text to make it clearer or more professional: ${content}` }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 150,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content;
    } else {
      console.error('No rephrased content returned from Groq API:', response.data);
      return content;  // Fallback to original content
    }
  } catch (error) {
    console.error('Error rephrasing text:', error);
    return content;  // Fallback to original content
  }
};

// Reminder Enhancements
export const suggestReminderTime = (content: string) => {
  // You can use regular expressions to extract dates and times, then suggest reminders based on content.
  // Example: If the content mentions "meeting at 3 PM," suggest a reminder 30 minutes prior.
  const meetingTime = content.match(/(\d{1,2}:\d{2}\s?(AM|PM))/i); // Simple regex for time extraction
  if (meetingTime) {
    const reminderTime = new Date();
    reminderTime.setHours(parseInt(meetingTime[1].split(':')[0]), parseInt(meetingTime[1].split(':')[1]) - 30);
    return `Reminder suggested for ${reminderTime.toLocaleString()}`;
  }
  return 'No reminder suggestion available';
};

// Smart Search (AI-powered Search)
export const aiSearch = async (query: string) => {
  // Use embeddings or semantic search to enhance search functionality
  // Example: Querying a list of notes and finding the most relevant ones using AI
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        { role: "system", content: "You are an assistant that performs AI-powered search." },
        { role: "user", content: `Search for notes related to: ${query}` }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 150,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content;
    } else {
      console.error('No search results returned from Groq API:', response.data);
      return 'No results found';
    }
  } catch (error) {
    console.error('Error performing AI search:', error);
    return 'Error searching for content';
  }
};

// Use this function to call the summarization logic
export const summarizeNote = async (content: string): Promise<AIResponse> => {
  const sanitizedContent = sanitizeInput(content);
  const maskedContent = maskSensitiveData(sanitizedContent);

  if (!sanitizedContent) {
    return getDefaultResponse();
  }

  try {

    const response = await axios.post(
      groqAPIUrl,
      {
        messages: [
          {
           role: "system",
            content: `You are a JSON-only response AI. You MUST respond with valid JSON only, no other text.

            Analyze the note and respond with this exact JSON structure:
            {
              "summary": "brief summary",
              "suggestedColor": "yellow",
              "mood": "neutral",
              "emoji": "📝",
              "categories": ["general"],
              "priority": 1,
              "actionItems": []
            }

            Rules:
            - suggestedColor must be one of: yellow, blue, green, pink, purple
            - priority must be a number 1-5
            - categories must be an array of strings
            - actionItems must be an array
            - NO explanatory text, ONLY valid JSON`
          },
          {
            role: "user",
            content: `Analyze this note and respond with JSON only: "${maskedContent}"`
          }
        ],
        model: "llama3-8b-8192",
        temperature: 0.5,
        max_tokens: 1024,
      },
      axiosConfig
    );

    // Validate response structure
    if (!response.data?.choices?.[0]?.message?.content) {
      console.error('Invalid API response:', response.data);
      return getDefaultResponse(maskedContent);
    }

    // Get the raw content and clean it
    let rawContent = response.data.choices[0].message.content.trim();

    // Try to extract JSON if it's wrapped in text
    const jsonMatch = rawContent.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      rawContent = jsonMatch[0];
    }

    // Safely parse JSON with validation
    let parsedResponse: AIResponse;
    try {
      parsedResponse = JSON.parse(rawContent);
      validateAIResponse(parsedResponse);
    } catch (parseError) {
      console.warn('AI returned non-JSON response, using smart defaults');
      console.log('Raw AI response:', rawContent.substring(0, 200));
      return getDefaultResponse(maskedContent);
    }

    return parsedResponse;

  } catch (error) {
    console.warn('AI processing failed, using smart defaults:', error);
    return getDefaultResponse(sanitizedContent);
  }
};

// Helper function to return smart default response based on content
function getDefaultResponse(content?: string): AIResponse {
  if (!content) {
    return {
      summary: 'Note created successfully',
      suggestedColor: 'yellow',
      mood: 'neutral',
      emoji: '📝',
      categories: ['general'],
      priority: 1,
      actionItems: []
    };
  }

  // Smart defaults based on content analysis
  const lowerContent = content.toLowerCase();
  let color: 'yellow' | 'blue' | 'green' | 'pink' | 'purple' = 'yellow';
  let emoji = '📝';
  let mood = 'neutral';
  let priority = 1;
  let categories = ['general'];

  // Simple keyword-based analysis
  if (lowerContent.includes('urgent') || lowerContent.includes('important') || lowerContent.includes('asap')) {
    priority = 5;
    color = 'pink';
    emoji = '🚨';
    mood = 'urgent';
    categories = ['urgent'];
  } else if (lowerContent.includes('work') || lowerContent.includes('meeting') || lowerContent.includes('project')) {
    color = 'blue';
    emoji = '💼';
    mood = 'focused';
    categories = ['work'];
  } else if (lowerContent.includes('idea') || lowerContent.includes('creative') || lowerContent.includes('design')) {
    color = 'purple';
    emoji = '💡';
    mood = 'creative';
    categories = ['ideas'];
  } else if (lowerContent.includes('goal') || lowerContent.includes('plan') || lowerContent.includes('todo')) {
    color = 'green';
    emoji = '🎯';
    mood = 'motivated';
    categories = ['goals'];
  }

  return {
    summary: content.length > 100 ? content.substring(0, 97) + '...' : content,
    suggestedColor: color,
    mood,
    emoji,
    categories,
    priority,
    actionItems: []
  };
}

// Response validation
function validateAIResponse(response: any): asserts response is AIResponse {
  const requiredFields = ['summary', 'suggestedColor', 'mood', 'emoji', 'categories', 'priority'];
  for (const field of requiredFields) {
    if (!(field in response)) {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  // Validate color is one of the allowed values
  if (!['yellow', 'blue', 'green', 'pink', 'purple'].includes(response.suggestedColor)) {
    response.suggestedColor = 'yellow'; // Default fallback
  }

  // Ensure priority is a number between 1-5
  if (typeof response.priority !== 'number' || response.priority < 1 || response.priority > 5) {
    response.priority = 1;
  }

  // Ensure categories is an array
  if (!Array.isArray(response.categories)) {
    response.categories = [];
  }

  // Ensure actionItems is an array
  if (!Array.isArray(response.actionItems)) {
    response.actionItems = [];
  }
}

// New function to find related notes
export async function findRelatedNotes(noteContent: string, otherNotes: string[]): Promise<number[]> {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        {
          role: "system",
          content: `You are an AI assistant that finds semantic relationships between notes. 
          Given a main note and a list of other notes, return the indices (0-based) of the 3 most related notes.
          Respond with only an array of numbers.`
        },
        {
          role: "user",
          content: `Main note: ${noteContent}
          Other notes: ${JSON.stringify(otherNotes)}`
        }
      ],
      model: "llama3-8b-8192",
      temperature: 0.3,
      max_tokens: 128,
    }, {
      headers: standardHeaders
    });

    const aiResponse = response.data.choices[0]?.message?.content;
    if (!aiResponse) throw new Error('No response from AI');

    // Parse the JSON response (should be an array of numbers)
    return JSON.parse(aiResponse);

  } catch (error) {
    console.error('AI Processing Error:', error);
    return [];
  }
}

// Add this type if not already defined
type NoteColor = 'yellow' | 'blue' | 'green' | 'pink' | 'purple';

// Add new function for color suggestion
export const suggestNoteColor = async (content: string): Promise<NoteColor> => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        {
          role: "system",
          content: "You are an assistant that suggests colors based on text content. Only respond with one of these colors: yellow, blue, green, pink, or purple. Choose yellow for happy/positive content, blue for professional/calm content, green for nature/growth related, pink for love/personal content, and purple for creative/artistic content."
        },
        {
          role: "user",
          content: `Suggest one color for this note content (respond with just the color name): ${content}`
        }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 10,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content as NoteColor;
    } else {
      console.error('No color suggestion returned from Groq API:', response.data);
      throw new Error('Failed to suggest a color');
    }
  } catch (error) {
    console.error('Error suggesting a color:', error);
    throw new Error('Failed to suggest a color');
  }
};

export const categorizeNote = async (content: string) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        {
          role: "system",
          content: "You are an assistant that categorizes notes. Return JSON with: mainCategory, subCategory, priority (1-5), and dueDate if mentioned."
        },
        {
          role: "user",
          content: `Categorize this note: ${content}`
        }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 150,
    }, {
      headers: standardHeaders
    });

    return JSON.parse(response.data.choices[0].message.content);
  } catch (error) {
    console.error('Error categorizing note:', error);
    return {
      mainCategory: 'Uncategorized',
      priority: 3
    };
  }
};

export const analyzeNoteContext = async (content: string, userContext: any) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        {
          role: "system",
          content: "You are an assistant that analyzes note context. Return JSON with location, timeOfDay, and mood."
        },
        {
          role: "user",
          content: `Analyze context: ${content}\nUser Context: ${JSON.stringify(userContext)}`
        }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 150,
    }, {
      headers: standardHeaders
    });

    return JSON.parse(response.data.choices[0].message.content);
  } catch (error) {
    console.error('Error analyzing context:', error);
    return {};
  }
};
