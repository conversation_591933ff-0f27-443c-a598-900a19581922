'use client';
    
import axios from 'axios';
import { maskSensitiveData } from './src/utils/security';

const groqAPIUrl = 'https://api.groq.com/openai/v1/chat/completions';  // Correct endpoint
const apiKey = process.env.NEXT_PUBLIC_GROQ_API_KEY;  // Make it accessible on client side
if (!apiKey) {
  throw new Error('GROQ API key is not configured');
}

const standardHeaders = {
  'Authorization': `Bearer ${apiKey}`,
  'Content-Type': 'application/json',
};

export interface AIResponse {
  summary: string;
  suggestedColor: 'yellow' | 'blue' | 'green' | 'pink' | 'purple';
  mood: string;
  emoji: string;
  categories: string[];
  priority: number;
  suggestedReminder?: string;
  actionItems: {
    task: string;
    dueDate?: string;
    priority: 'high' | 'medium' | 'low';
  }[];
}

// 2. Add input sanitization
const sanitizeInput = (content: string): string => {
  // Remove any potentially dangerous characters or patterns
  return content.replace(/<[^>]*>?/gm, '');
};

// 3. Add rate limiting headers and timeout
const axiosConfig = {
  headers: {
    ...standardHeaders,
    'X-Request-ID': crypto.randomUUID(), // Add request tracking
  },
  timeout: 10000, // 10 second timeout
};

// Function to call Groq's summarization API
export const summarizeWithGroq = async (content: string) => {
  try {
    const response = await axios.post(
      groqAPIUrl,
      {
        messages: [
          {
            role: "system",
            content: "You are a friendly assistant that provides short, clear, and concise summaries."
          },
          {
            role: "user",
            content: `Please summarize this text in a short and concise manner: ${content}`
          }
        ],
        model: "llama3-8b-8192",  // or "llama2-70b-4096"
        temperature: 0.5,
        max_tokens: 100,  // Limit the length of the response
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
      }
    );

    // Ensure the API response contains the summary message
    if (response.data.choices && response.data.choices[0].message.content) {
      const summary = response.data.choices[0].message.content;
      
      // Return only the concise summary without extra text
      return summary;
    } else {
      console.error('No summary returned from Groq API:', response.data);
      return 'Summary unavailable'; // Fallback if no summary is returned
    }
  } catch (error) {
    console.error('Error with Groq AI summarization:', error);
    throw new Error('Failed to summarize the content with Groq');
  }
};

// Sentiment Analysis (Mood Detection)
export const analyzeSentiment = async (content: string) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        { role: "system", content: "You are an assistant that analyzes the mood of the content." },
        { role: "user", content: `Analyze the sentiment of this text: ${content}` }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 100,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content;
    } else {
      console.error('No sentiment returned from Groq API:', response.data);
      return 'Neutral';  // Default sentiment if none returned
    }
  } catch (error) {
    console.error('Error with sentiment analysis:', error);
    return 'Error analyzing sentiment';
  }
};

// Context-Based Suggestions (Smart Suggestions)
export const suggestActionsBasedOnContent = async (content: string) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        { role: "system", content: "You are an assistant that provides smart suggestions based on note content." },
        { role: "user", content: `Based on this note, what actions or suggestions can you provide? Content: ${content}` }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 150,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content;
    } else {
      console.error('No suggestions returned from Groq API:', response.data);
      return 'No suggestions available';
    }
  } catch (error) {
    console.error('Error with action suggestions:', error);
    return 'Error generating suggestions';
  }
};

// Tagging and Organization (AI-Generated Tags)
export const generateTagsFromContent = async (content: string) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        { role: "system", content: "You are an assistant that generates tags based on note content." },
        { role: "user", content: `Please generate tags for this note: ${content}` }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 100,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content.split(',');  // Return tags as an array
    } else {
      console.error('No tags returned from Groq API:', response.data);
      return ['Uncategorized'];
    }
  } catch (error) {
    console.error('Error generating tags:', error);
    return ['Error generating tags'];
  }
};

// Actionable To-Do Items (Task Extraction)
export const extractTasksFromNote = async (content: string) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        { role: "system", content: "You are an assistant that extracts tasks from note content." },
        { role: "user", content: `Please extract tasks from this note: ${content}` }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 150,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content.split(',');  // Return tasks as an array
    } else {
      console.error('No tasks extracted from Groq API:', response.data);
      return ['No tasks identified'];
    }
  } catch (error) {
    console.error('Error extracting tasks:', error);
    return ['Error extracting tasks'];
  }
};

// Quick Rewrites or Improvements (Enhance Writing)
export const rephraseText = async (content: string) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        { role: "system", content: "You are an assistant that helps improve the writing." },
        { role: "user", content: `Please rephrase the following text to make it clearer or more professional: ${content}` }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 150,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content;
    } else {
      console.error('No rephrased content returned from Groq API:', response.data);
      return content;  // Fallback to original content
    }
  } catch (error) {
    console.error('Error rephrasing text:', error);
    return content;  // Fallback to original content
  }
};

// Reminder Enhancements
export const suggestReminderTime = (content: string) => {
  // You can use regular expressions to extract dates and times, then suggest reminders based on content.
  // Example: If the content mentions "meeting at 3 PM," suggest a reminder 30 minutes prior.
  const meetingTime = content.match(/(\d{1,2}:\d{2}\s?(AM|PM))/i); // Simple regex for time extraction
  if (meetingTime) {
    const reminderTime = new Date();
    reminderTime.setHours(parseInt(meetingTime[1].split(':')[0]), parseInt(meetingTime[1].split(':')[1]) - 30);
    return `Reminder suggested for ${reminderTime.toLocaleString()}`;
  }
  return 'No reminder suggestion available';
};

// Smart Search (AI-powered Search)
export const aiSearch = async (query: string) => {
  // Use embeddings or semantic search to enhance search functionality
  // Example: Querying a list of notes and finding the most relevant ones using AI
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        { role: "system", content: "You are an assistant that performs AI-powered search." },
        { role: "user", content: `Search for notes related to: ${query}` }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 150,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content;
    } else {
      console.error('No search results returned from Groq API:', response.data);
      return 'No results found';
    }
  } catch (error) {
    console.error('Error performing AI search:', error);
    return 'Error searching for content';
  }
};

// Use this function to call the summarization logic
export const summarizeNote = async (content: string): Promise<AIResponse> => {
  try {
    const sanitizedContent = sanitizeInput(content);
    const maskedContent = maskSensitiveData(sanitizedContent);

       if (!sanitizedContent) {
      return {
        summary: '',
        suggestedColor: 'yellow',
        mood: 'neutral',
        emoji: '📝',
        categories: [],
        priority: 1,
        actionItems: []
      };
    }


    const response = await axios.post(
      groqAPIUrl,
      {
        messages: [
          {
           role: "system",
            content: `You are an AI assistant that analyzes notes securely.
            Never include or expose:
            - ID numbers
            - Phone numbers
            - Email addresses
            - Financial information
            - Personal addresses
            - Any sensitive personal data
            
            Always mask such information with asterisks (*).
            
            Respond with a JSON object containing exactly these fields:
            {
              "summary": "A brief, sanitized summary without sensitive data",
              "suggestedColor": "one of: yellow, blue, green, pink, purple",
              "mood": "the emotional tone of the note",
              "emoji": "a single relevant emoji",
              "categories": ["array", "of", "relevant", "categories"],
              "priority": "number from 1-5",
              "actionItems": [{"task": "task description", "priority": "high/medium/low"}]
            }`
          },
          {
            role: "user",
            content: `Read and analyze the following note carefully:
            "${sanitizedContent}"

            Provide a natural response based on the note's actual content:
            - For urgent or important matters, mark as high priority
            - For casual or general notes, mark as normal priority
            - Choose yellow for happy/positive content
            - Choose blue for work/professional content
            - Choose green for plans/goals
            - Choose pink for creative/fun content
            - Choose purple for deep/reflective content
            - Select a mood that matches the writing tone (e.g., excited, worried, focused, relaxed)
            - Pick an emoji that directly relates to the main topic
            - Write a brief but specific summary capturing the key points

            Important: Your response should vary based on the actual content of each note.`
          }
        ],
        model: "llama3-8b-8192",
        temperature: 0.5,
        max_tokens: 1024,
      },
      axiosConfig
    );

    // 5. Validate response structure
    if (!response.data?.choices?.[0]?.message?.content) {
      throw new Error('Invalid API response structure');
    }

    // 6. Safely parse JSON with validation
    let parsedResponse: AIResponse;
    try {
      parsedResponse = JSON.parse(response.data.choices[0].message.content);
      validateAIResponse(parsedResponse); // Add your validation function
    } catch (e) {
      throw new Error('Invalid response format');
    }

    return parsedResponse;

  } catch (error) {
    // 7. Safe error handling without exposing internals
    throw new Error('Failed to process note');
  }
};

// 8. Add response validation
function validateAIResponse(response: any): asserts response is AIResponse {
  const requiredFields = ['summary', 'suggestedColor', 'mood', 'emoji', 'categories', 'priority'];
  for (const field of requiredFields) {
    if (!(field in response)) {
      throw new Error(`Missing required field: ${field}`);
    }
  }
  
  // Validate color is one of the allowed values
  if (!['yellow', 'blue', 'green', 'pink', 'purple'].includes(response.suggestedColor)) {
    throw new Error('Invalid color value');
  }
}

// New function to find related notes
export async function findRelatedNotes(noteContent: string, otherNotes: string[]): Promise<number[]> {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        {
          role: "system",
          content: `You are an AI assistant that finds semantic relationships between notes. 
          Given a main note and a list of other notes, return the indices (0-based) of the 3 most related notes.
          Respond with only an array of numbers.`
        },
        {
          role: "user",
          content: `Main note: ${noteContent}
          Other notes: ${JSON.stringify(otherNotes)}`
        }
      ],
      model: "llama3-8b-8192",
      temperature: 0.3,
      max_tokens: 128,
    }, {
      headers: standardHeaders
    });

    const aiResponse = response.data.choices[0]?.message?.content;
    if (!aiResponse) throw new Error('No response from AI');

    // Parse the JSON response (should be an array of numbers)
    return JSON.parse(aiResponse);

  } catch (error) {
    console.error('AI Processing Error:', error);
    return [];
  }
}

// Add this type if not already defined
type NoteColor = 'yellow' | 'blue' | 'green' | 'pink' | 'purple';

// Add new function for color suggestion
export const suggestNoteColor = async (content: string): Promise<NoteColor> => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        {
          role: "system",
          content: "You are an assistant that suggests colors based on text content. Only respond with one of these colors: yellow, blue, green, pink, or purple. Choose yellow for happy/positive content, blue for professional/calm content, green for nature/growth related, pink for love/personal content, and purple for creative/artistic content."
        },
        {
          role: "user",
          content: `Suggest one color for this note content (respond with just the color name): ${content}`
        }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 10,
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.data.choices && response.data.choices[0].message.content) {
      return response.data.choices[0].message.content as NoteColor;
    } else {
      console.error('No color suggestion returned from Groq API:', response.data);
      throw new Error('Failed to suggest a color');
    }
  } catch (error) {
    console.error('Error suggesting a color:', error);
    throw new Error('Failed to suggest a color');
  }
};

export const categorizeNote = async (content: string) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        {
          role: "system",
          content: "You are an assistant that categorizes notes. Return JSON with: mainCategory, subCategory, priority (1-5), and dueDate if mentioned."
        },
        {
          role: "user",
          content: `Categorize this note: ${content}`
        }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 150,
    }, {
      headers: standardHeaders
    });

    return JSON.parse(response.data.choices[0].message.content);
  } catch (error) {
    console.error('Error categorizing note:', error);
    return {
      mainCategory: 'Uncategorized',
      priority: 3
    };
  }
};

export const analyzeNoteContext = async (content: string, userContext: any) => {
  try {
    const response = await axios.post(groqAPIUrl, {
      messages: [
        {
          role: "system",
          content: "You are an assistant that analyzes note context. Return JSON with location, timeOfDay, and mood."
        },
        {
          role: "user",
          content: `Analyze context: ${content}\nUser Context: ${JSON.stringify(userContext)}`
        }
      ],
      model: "llama3-8b-8192",
      temperature: 0.5,
      max_tokens: 150,
    }, {
      headers: standardHeaders
    });

    return JSON.parse(response.data.choices[0].message.content);
  } catch (error) {
    console.error('Error analyzing context:', error);
    return {};
  }
};
