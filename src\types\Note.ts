// // export interface Note {
// //   id: string;
// //   user_id: string;            // The ID of the user who owns the note
// //   title: string;              // Plaintext title for the note (if you want to display it)
// //   content: string;            // Plaintext content for the note (if you want to display it)
// //   encrypted_title: Buffer;   // The encrypted version of the title
// //   encrypted_content: Buffer; // The encrypted version of the content
// // }

// // Define types for the sticky notes
// interface StickyNote {
//   id: number;
//   date: string;
//   title: string;
//   content: string;
//   user_id: string;
//   reminder_time: Date | null;
//   summary?: string;
//   color?: NoteColor;
//   created_at?: string | null;
//   updated_at: string | null;
//   mood?: string;
//   emoji?: string;
//   ai_categories?: string[];
//   priority?: number;
//   actionItems?: ActionItem[];
//   is_archived: boolean;
// }

// type NoteColor = 'yellow' | 'blue' | 'green' | 'pink' | 'purple';

// // Update the ActionItem interface
// interface ActionItem {
//   task: string;
//   dueDate?: string; // Changed from Date to string
//   priority: 'high' | 'medium' | 'low';
// }

// // Add this interface near other interfaces
// interface SummaryModalProps {
//   isOpen: boolean;
//   onClose: () => void;
//   summary: string;
//   title: string;
//   color?: NoteColor;
// }

// // Add this near other interface definitions
// interface ExportDropdownProps {
//   notes: StickyNote[];
// }

//  // Define an interface for the imported note structure
//  interface ImportedNote {
//   title: string;
//   content: string;
//   summary?: string;
//   date?: string;
//   // Add other fields as needed
// }
