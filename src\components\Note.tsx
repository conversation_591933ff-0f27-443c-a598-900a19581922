'use client';
    
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface NoteProps {
  title: string;
  content: string;
  onClick: () => void;
}

const Note: React.FC<NoteProps> = ({ title, content, onClick }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div 
        onClick={onClick}
        className="cursor-pointer p-4 border rounded hover:bg-gray-100"
        role="button"
      >
        <h3>{title}</h3>
        <p>{content}</p>
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsOpen(false)}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
            >
              <h2 className="text-2xl font-bold mb-4">{title}</h2>
              <p className="whitespace-pre-wrap">{content}</p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Note; 