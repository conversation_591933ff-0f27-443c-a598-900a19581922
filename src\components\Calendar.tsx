// // components/Calendar.tsx
// import React, { useState } from 'react'
// import Calendar from 'react-calendar'
// import 'react-calendar/dist/Calendar.css'

// const CalendarComponent = () => {
//   const [selectedDate, setSelectedDate] = useState(new Date())

//   const onDateChange = (date: Date) => setSelectedDate(date)

//   return (
//     <div className="w-full max-w-md p-4 bg-white rounded-lg shadow-lg">
//       <h2 className="text-xl font-semibold text-center mb-4">Select a date</h2>
      
//       <Calendar
//         onChange={onDateChange}
//         value={selectedDate}
//         className="rounded-lg shadow-md"
//       />
      
//       <div className="mt-4 text-center">
//         <h3 className="text-lg font-medium">Selected Date:</h3>
//         <p className="text-sm text-gray-600">{selectedDate.toDateString()}</p>
//       </div>
//     </div>
//   )
// }

// export default CalendarComponent
