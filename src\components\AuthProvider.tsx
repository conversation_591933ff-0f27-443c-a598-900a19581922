'use client';
import { useEffect, useState } from 'react'
import { supabase } from '../../supabase'
import { usePathname, useRouter } from 'next/navigation'
import { AuthChangeEvent, Session } from '@supabase/supabase-js'

export default function AuthProvider({ children }: { children: React.ReactNode }) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_user, setUser] = useState<null | { id: string; email: string }>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const fetchSession = async () => {
      try {
        setLoading(true)
        const { data, error } = await supabase.auth.getSession()
        
        if (error) {
          setError(error.message)
          setLoading(false)
          return
        }

        if (data?.session) {
          setUser({
            id: data.session.user.id,
            email: data.session.user.email || '',
          })
          if (pathname === '/auth') {
            router.push('/') // Redirect to home if logged in and on auth page
          }
        } else if (pathname !== '/auth') {
          router.push('/auth') // Redirect to auth if no session and not on auth page
        }

        setLoading(false)
      } catch (err) {
        setError('An error occurred while checking the session.')
        console.error(err)
        setLoading(false)
      }
    }

    fetchSession()

    const { data: { subscription } } = supabase.auth.onAuthStateChange((
      event: AuthChangeEvent,
      session: Session | null
    ) => {
      if (event === 'SIGNED_IN') {
        setUser(session?.user ? { 
          id: session.user.id, 
          email: session.user.email || '' 
        } : null)
        
        if (pathname === '/auth') {
          router.push('/')
        }
      } else if (event === 'SIGNED_OUT') {
        setUser(null)
        if (pathname !== '/auth') {
          router.push('/auth')
        }
      }
    })

    return () => {
      subscription?.unsubscribe()
    }
  }, [pathname, router])

  if (loading) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>
  }

  if (error) {
    return <div className="flex justify-center items-center min-h-screen text-red-500">{error}</div>
  }

  return <>{children}</>
}
