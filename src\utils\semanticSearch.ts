import { createEmbedding, cosineSimilarity } from './aiUtils.js';

interface Note {
  embedding: number[];
  // add other properties of Note if needed
}

export const semanticSearch = async (query: string, notes: Note[]) => {
  const queryEmbedding = await createEmbedding(query);
  
  return notes.map(note => ({
    note,
    similarity: cosineSimilarity(queryEmbedding, note.embedding)
  }))
  .sort((a, b) => b.similarity - a.similarity)
  .slice(0, 5);
};