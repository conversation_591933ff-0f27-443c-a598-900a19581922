import { createClient } from '@supabase/supabase-js';
import type { NextApiRequest, NextApiResponse } from 'next';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Simple connection test
    const { data: authData, error: authError } = await supabase.auth.getSession();

    if (authError) {
      return res.status(500).json({
        error: 'Auth connection failed',
        details: authError.message
      });
    }

    // Test 2: Try to query the sticky_notes table
    const { data: notes, error: notesError } = await supabase
      .from('sticky_notes')
      .select('id')
      .limit(1);

    if (notesError) {
      console.error('Notes query error:', notesError);
      return res.status(500).json({
        error: 'Table query failed',
        details: {
          message: notesError.message,
          code: notesError.code,
          hint: notesError.hint
        }
      });
    }

    return res.status(200).json({
      message: 'Database connection successful',
      tableExists: true,
      notesCount: notes?.length || 0
    });

  } catch (error) {
    console.error('Database test error:', error);
    return res.status(500).json({
      error: 'Database test failed',
      details: error
    });
  }
}
