import { randomBytes, createCipher<PERSON>, createDecipheriv } from 'crypto';
// import { ReactNode } from 'react';

export interface EncryptionConfig {
  masterKey: string;
  keyIterations?: number;
  saltLength?: number;
}

export interface EncryptedNote {
  id?: number;
  user_id: string;
  encrypted_title: string;
  encrypted_content: string;
  created_at?: Date;
}

export interface DecryptedNote {
  id?: number;
  title: string;
  content: string;
  user_id: string;
  color: string;
  emoji: string;
  mood: string;
  ai_categories: string[];
  priority: number;
  actionItems: boolean;
  summary: string;
  reminder_time: Date | null;
  created_at?: Date;
  updated_at: string;
  is_archived: boolean;
}

export class AdvancedEncryption {
  private readonly algorithm = 'aes-256-cbc';
  private readonly keyLength = 32;
  private readonly ivLength = 16;

  constructor(private config: { masterKey: string }) {
    if (!config.masterKey) throw new Error('Master key required');
  }

  private bytesToBuffer(bytes: any): Buffer {
    // Handle Postgres BYTEA format
    if (typeof bytes === 'string' && bytes.startsWith('\\x')) {
      return Buffer.from(bytes.slice(2), 'hex');
    }
    return Buffer.from(bytes);
  }

  public encryptNote(title: string, content: string, userId: string) {
    try {
      const key = Buffer.from(this.config.masterKey, 'hex');
      const iv = randomBytes(this.ivLength);
      
      const titleCipher = createCipheriv(this.algorithm, key, iv);
      const contentCipher = createCipheriv(this.algorithm, key, iv);

      const encryptedTitle = Buffer.concat([
        iv,
        titleCipher.update(title, 'utf8'),
        titleCipher.final()
      ]);

      const encryptedContent = Buffer.concat([
        iv,
        contentCipher.update(content, 'utf8'),
        contentCipher.final()
      ]);

      return {
        encrypted_title: '\\x' + encryptedTitle.toString('hex'),
        encrypted_content: '\\x' + encryptedContent.toString('hex'),
        salt: '\\x' + randomBytes(16).toString('hex'),
        user_id: userId
      };
    } catch  {
      // console.error('Encryption error:', error);
      throw new Error('Failed to encrypt note');
    }
  }

  public decryptNote(note: any) {
    try {
      // console.log('Decrypting note:', {
      //   id: note.id,
      //   titleFormat: note.encrypted_title?.substring(0, 20),
      //   contentFormat: note.encrypted_content?.substring(0, 20)
      // });

      const key = Buffer.from(this.config.masterKey, 'hex');
      const titleData = this.bytesToBuffer(note.encrypted_title);
      const contentData = this.bytesToBuffer(note.encrypted_content);

      const titleIv = titleData.slice(0, this.ivLength);
      const contentIv = contentData.slice(0, this.ivLength);

      const titleDecipher = createDecipheriv(this.algorithm, key, titleIv);
      const contentDecipher = createDecipheriv(this.algorithm, key, contentIv);

      const title = titleDecipher.update(titleData.slice(this.ivLength)) + 
                   titleDecipher.final('utf8');
      
      const content = contentDecipher.update(contentData.slice(this.ivLength)) + 
                     contentDecipher.final('utf8');

      return { title, content };
    } 
    catch (error) {
      console.error('Decryption error:', {
        error,
        noteId: note?.id,
        hasTitleData: !!note?.encrypted_title,
        hasContentData: !!note?.encrypted_content,
        titleLength: note?.encrypted_title?.length,
        contentLength: note?.encrypted_content?.length
      });
      throw new Error('Failed to decrypt note data');
    }
  }
}

export const encryptionService = new AdvancedEncryption({ masterKey: process.env.NEXT_PUBLIC_ENCRYPTION_MASTER_KEY || '' });