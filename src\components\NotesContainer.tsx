// import React, { useState } from 'react';

// interface Note {
//   id: string;
//   content: string;
//   position?: { x: number; y: number };
// }

// export const NotesContainer = () => {
//   const [currentNotes, setCurrentNotes] = useState<Note[]>([]);
//   const [isArranging, setIsArranging] = useState(false);

//   const updateNotePositions = (positions: { [key: string]: { x: number; y: number } }) => {
//     setCurrentNotes(notes => notes.map(note => ({
//       ...note,
//       position: positions[note.id]
//     })));
//   };

//   const arrangeNotesWithAI = async () => {
//     try {
//       setIsArranging(true);
//       const response = await fetch('/api/arrange-notes', {
//         method: 'POST',
//         body: JSON.stringify({ notes: currentNotes })
//       });
      
//       if (!response.ok) {
//         throw new Error('Failed to arrange notes');
//       }

//       const { positions } = await response.json();
//       updateNotePositions(positions);
//     } catch (error) {
//       console.error('Error arranging notes:', error);
//       // You might want to show an error toast/notification here
//     } finally {
//       setIsArranging(false);
//     }
//   };

//   return (
//     <div className="relative min-h-[500px] p-4">
//       <button 
//         onClick={arrangeNotesWithAI}
//         disabled={isArranging || currentNotes.length === 0}
//         className={`px-4 py-2 rounded-lg ${
//           isArranging 
//             ? 'bg-gray-400 cursor-not-allowed' 
//             : 'bg-blue-500 hover:bg-blue-600'
//         } text-white`}
//       >
//         {isArranging ? 'Arranging...' : '✨ Magic Arrange'}
//       </button>

//       {/* Display the notes */}
//       {currentNotes.map(note => (
//         <div
//           key={note.id}
//           style={{
//             position: 'absolute',
//             left: note.position?.x ?? 0,
//             top: note.position?.y ?? 0,
//             transition: 'all 0.3s ease'
//           }}
//           className="p-4 bg-white shadow-lg rounded-lg"
//         >
//           {note.content}
//         </div>
//       ))}
//     </div>
//   );
// }; 