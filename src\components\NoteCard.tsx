import { supabase } from "../../supabase";
import { DecryptedNote } from '../types/notes';

interface NoteCardProps {
  note: DecryptedNote;
}

const NoteCard: React.FC<NoteCardProps> = async ({ note }) => {
  if (!note) return null;

  const { data: { user } } = await supabase.auth.getUser();
  const userId = user?.id;

  const handleShare = async () => {
    try {
      // Create a shareable link using Supabase RLS policies
      const { data, error } = await supabase
        .from('shared_notes')
        .insert({
          note_id: note.id,
          shared_by: userId,
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days expiry
        })
        .select('id')
        .single();

      if (error) throw error;

      // Create shareable link
      const shareUrl = `${window.location.origin}/shared/${data.id}`;

      // Use Web Share API if available
      if (navigator.share) {
        await navigator.share({
          title: note.title,
          text: note.content,
          url: shareUrl,
        });
      } else {
        // Fallback to copying to clipboard
        await navigator.clipboard.writeText(shareUrl);
        alert('Share link copied to clipboard!');
      }
    } catch (error) {
      console.error('Error sharing note:', error);
    }
  };

  return (
    <div className="p-4 border rounded shadow-sm">
      <h3 className="font-bold">{note.title || 'Untitled'}</h3>
      <p>{note.content || 'No content'}</p>
      <div className="mt-2 text-sm text-gray-500">
        <span>{note.emoji}</span>
        <span>{new Date(note.created_at).toLocaleDateString()}</span>
      </div>
      <button onClick={() => handleShare()}>Share Note</button>
    </div>
  );
};

export default NoteCard;