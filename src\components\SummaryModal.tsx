'use client';
import { Dialog } from "@headlessui/react";
import {NoteColor }  from '../types/notes';  // Adjust import path as needed

interface SummaryModalProps {
  isOpen: boolean;
  onClose: () => void;
  summary: string;
  title: string;
  color?: NoteColor;
}

export const SummaryModal: React.FC<SummaryModalProps> = ({ 
  isOpen, 
  onClose, 
  summary, 
  title, 
  color = 'yellow' 
}) => {
  const getColorValue = (color: NoteColor) => {
    const colors: Record<NoteColor, string> = {
      yellow: 'linear-gradient(135deg, #fff7c4 10%, #ffd700 100%)',
      blue: 'linear-gradient(135deg, #c4f0ff 10%, #1e90ff 100%)',
      green: 'linear-gradient(135deg, #c4ffd4 10%, #32cd32 100%)',
      pink: 'linear-gradient(135deg, #ffc4e6 10%, #ff69b4 100%)'
    };
    return colors[color] || colors['yellow'];
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel 
          className="w-full max-w-md transform overflow-hidden rounded-lg p-6 text-left align-middle shadow-xl transition-all rotate-1"
          style={{
            background: getColorValue(color),
            boxShadow: '3px 3px 10px rgba(0,0,0,0.2)',
            transform: 'rotate(1deg)',
          }}
        >
          <Dialog.Title className="text-xl font-handwritten font-semibold mb-4 text-gray-900">
            {title}
          </Dialog.Title>
          
          <div className="mt-2">
            <p className="font-handwritten text-gray-900 text-base leading-relaxed whitespace-pre-wrap">
              {summary}
            </p>
          </div>

          <div className="mt-6 flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-white/50 hover:bg-white/70 rounded-md text-sm font-handwritten 
              text-gray-900 transition-colors duration-200 transform hover:scale-105"
            >
              Close
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};